"""Whisper模型辅助工具"""
import whisper
import torch
from gpu_utils import get_optimal_device, clear_gpu_cache

def safe_load_whisper_model(model_name="small"):
    """安全加载Whisper模型，自动处理设备和数据类型"""
    print(f"Loading Whisper model: {model_name}...")
    
    try:
        # 首先尝试正常加载
        device = get_optimal_device()
        
        if device == 'cuda':
            # GPU模式 - 使用float32以避免兼容性问题
            model = whisper.load_model(model_name, device="cuda")
            model = model.float()  # 使用FP32而不是FP16
            print("✓ Model loaded on GPU with FP32")
        else:
            # CPU模式
            model = whisper.load_model(model_name, device="cpu")
            model = model.float()  # 确保使用FP32
            print("✓ Model loaded on CPU with FP32")
            
        return model, device
        
    except Exception as e:
        print(f"Error loading model: {e}")
        print("Attempting CPU-only mode...")
        
        # 强制CPU模式
        model = whisper.load_model(model_name, device="cpu")
        model = model.float()  # 确保FP32
        print("✓ Model loaded on CPU (fallback)")
        
        return model, 'cpu'

def safe_transcribe(model, audio_path, device='cpu', language=None):
    """安全转录音频，自动处理设备兼容性问题

    Args:
        model: Whisper模型
        audio_path: 音频文件路径
        device: 设备类型 ('cpu' 或 'cuda')
        language: 指定语言代码，如果为None则自动检测
    """
    try:
        # 统一使用float32以避免兼容性问题
        if next(model.parameters()).dtype != torch.float32:
            model = model.float()

        # 根据设备选择合适的参数
        if device == 'cuda' and torch.cuda.is_available():
            # GPU模式 - 确保模型在GPU上
            if next(model.parameters()).device.type != 'cuda':
                model = model.cuda()

            # 如果指定了语言，使用指定语言；否则让Whisper自动检测
            if language:
                result = model.transcribe(
                    audio_path,
                    language=language,
                    fp16=False,  # 禁用FP16以避免兼容性问题
                    beam_size=5,
                    best_of=5
                )
            else:
                result = model.transcribe(
                    audio_path,
                    fp16=False,  # 禁用FP16以避免兼容性问题
                    beam_size=5,
                    best_of=5
                )
        else:
            # CPU模式 - 确保模型在CPU上
            if next(model.parameters()).device.type != 'cpu':
                model = model.cpu()

            # 如果指定了语言，使用指定语言；否则让Whisper自动检测
            if language:
                result = model.transcribe(
                    audio_path,
                    language=language,
                    fp16=False,  # CPU不支持FP16
                    beam_size=5,
                    best_of=5
                )
            else:
                result = model.transcribe(
                    audio_path,
                    fp16=False,  # CPU不支持FP16
                    beam_size=5,
                    best_of=5
                )

        return result
        
    except (RuntimeError, TypeError) as e:
        error_str = str(e)
        if "mixed dtype" in error_str or "half" in error_str or "Float" in error_str:
            print(f"Data type mismatch detected: {error_str}")
            print("Converting model to CPU with float32...")
            
            # 强制转换模型到CPU并使用float32
            model = model.cpu()
            model = model.float()
            
            # 清理GPU缓存
            if torch.cuda.is_available():
                clear_gpu_cache()
            
            # 重试转录
            if language:
                result = model.transcribe(
                    audio_path,
                    language=language,
                    fp16=False,
                    beam_size=5,
                    best_of=5
                )
            else:
                result = model.transcribe(
                    audio_path,
                    fp16=False,
                    beam_size=5,
                    best_of=5
                )
            
            return result
        else:
            raise e