#!/usr/bin/env python3
"""测试时间戳功能"""

import os
from datetime import datetime
from werkzeug.utils import secure_filename

def test_timestamp_filename():
    """测试时间戳文件名生成"""
    
    # 模拟原始文件名
    test_filenames = [
        "test_audio.mp3",
        "会议录音.wav",
        "presentation.m4a",
        "interview-2024.mp4"
    ]
    
    print("=== 时间戳文件名测试 ===")
    
    for original_filename in test_filenames:
        # 生成带时间戳的文件名（模拟app_with_ai.py的逻辑）
        secure_name = secure_filename(original_filename)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:-3]  # 精确到毫秒

        # 处理中文文件名或secure_filename处理后为空的情况
        name_part = os.path.splitext(secure_name)[0]
        ext_part = os.path.splitext(secure_name)[1]
        if not name_part or name_part.strip() == '':
            # 如果secure_filename处理后名称为空，使用通用名称
            ext = os.path.splitext(original_filename)[1].lower()
            secure_name = f"audio_file{ext}"

        name, ext = os.path.splitext(secure_name)
        timestamped_filename = f"{name}_{timestamp}{ext}"
        
        print(f"原始文件名: {original_filename}")
        print(f"安全文件名: {secure_name}")
        print(f"时间戳文件名: {timestamped_filename}")
        print("-" * 50)
        
        # 模拟输出文件名生成
        base_name = os.path.splitext(timestamped_filename)[0]
        txt_output = f"{base_name}.txt"
        srt_output = f"{base_name}.srt"
        
        print(f"TXT输出: {txt_output}")
        print(f"SRT输出: {srt_output}")
        print("=" * 50)

def test_dual_subtitle_timestamp():
    """测试双语字幕时间戳"""
    print("\n=== 双语字幕时间戳测试 ===")
    
    for i in range(3):
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:-3]
        filename = f"dual_subtitle_{timestamp}.srt"
        print(f"双语字幕文件名 {i+1}: {filename}")

if __name__ == "__main__":
    test_timestamp_filename()
    test_dual_subtitle_timestamp()
    print("\n✅ 时间戳功能测试完成")
