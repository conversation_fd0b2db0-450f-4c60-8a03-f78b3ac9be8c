1
00:00:00,000 --> 00:00:01,600
Have you ever stopped to think about
你可曾驻足思考过

2
00:00:01,600 --> 00:00:06,400
how these really tiny, totally invisible changes
这些极其微小、完全不可见的变化

3
00:00:06,400 --> 00:00:08,520
happen at the molecular level?
是如何在分子层面发生的？

4
00:00:08,520 --> 00:00:11,120
Inside proteins specifically,
特别是在蛋白质内部

5
00:00:11,120 --> 00:00:14,680
and how those tiny shifts can have just massive impacts
这些细微变化如何产生巨大影响

6
00:00:14,680 --> 00:00:18,520
on health, medicine, even industry, it's kind of wild.
波及健康、医疗乃至工业领域，这很神奇

7
00:00:18,520 --> 00:00:19,360
It really is.
确实如此

8
00:00:19,360 --> 00:00:21,560
Protein stability, that's the bedrock, right?
蛋白质稳定性是基础，对吧？

9
00:00:21,560 --> 00:00:24,040
How well it holds its shape is crucial for what it does.
其结构保持能力决定功能发挥

10
00:00:24,040 --> 00:00:26,000
Exactly, and here's the kicker.
没错，关键点在这里

11
00:00:26,000 --> 00:00:28,840
Even changing one single amino acid,
即便只改变一个氨基酸

12
00:00:28,840 --> 00:00:30,560
just one tiny building block
这个微小结构单元

13
00:00:30,560 --> 00:00:32,520
can throw that stability way off.
就足以破坏稳定性

14
00:00:32,520 --> 00:00:35,240
Yeah, completely change its function, or lack thereof.
是的，彻底改变或丧失其功能

15
00:00:35,240 --> 00:00:37,560
So the big crooked the puzzle scientists have wrestled with
科学家们长期攻克的难题是

16
00:00:37,560 --> 00:00:39,800
is how do we actually predict those changes,
如何准确预测这些变化

17
00:00:39,800 --> 00:00:42,160
and importantly, their consequences.
更重要的是预判其影响

18
00:00:42,160 --> 00:00:43,400
It's been a tough nut to crack.
这曾是棘手难题

19
00:00:43,400 --> 00:00:45,760
Well, that's exactly what we're getting into today.
这正是我们今天要探讨的

20
00:00:45,760 --> 00:00:47,080
This deep dive is centered
本次深度解析聚焦于

21
00:00:47,080 --> 00:00:49,760
on a really fascinating new research paper.
关于一篇非常引人入胜的新研究论文。

22
00:00:49,760 --> 00:00:52,640
It introduces a computational method called DP-STAB.
它介绍了一种名为DP-STAB的计算方法。

23
00:00:52,640 --> 00:00:53,680
DP-STAB, right.
对，就是DP-STAB。

24
00:00:53,680 --> 00:00:55,640
And we're gonna explore how this new approach
我们将探索这种新方法如何可能最终破解。

25
00:00:55,640 --> 00:00:57,640
might finally be cracking.
其中一个关键而复杂的挑战。

26
00:00:57,920 --> 00:01:00,080
One of elective biology's really critical,
在选择性生物学中。

27
00:01:00,080 --> 00:01:01,560
complex challenges.
复杂挑战。

28
00:01:01,560 --> 00:01:03,200
Yeah, and our mission here really
是的，我们的任务就是。

29
00:01:03,200 --> 00:01:05,560
is to unpack DP-STAB for you.
为你解析DP-STAB。

30
00:01:05,560 --> 00:01:07,560
Whether you're prepping for a meeting
无论你是在为会议做准备。

31
00:01:07,560 --> 00:01:10,080
or just curious about the latest science.
还是对最新科学感到好奇。

32
00:01:10,080 --> 00:01:13,000
Or maybe just fascinated by how AI is changing things.
或者只是对AI如何改变事物感到着迷。

33
00:01:13,000 --> 00:01:14,000
Exactly.
没错。

34
00:01:14,000 --> 00:01:16,600
We wanna show you how DP-STAB predicts the effects
我们想向你展示DP-STAB如何预测。

35
00:01:16,600 --> 00:01:18,720
of these single mutations on stability,
这些单点突变对稳定性的影响。

36
00:01:18,720 --> 00:01:21,000
and crucially, why that actually matters.
关键是，为什么这实际上很重要。

37
00:01:21,000 --> 00:01:23,320
Because it's not just academic curiosity, is it?
因为这不仅仅是学术上的好奇心，对吧？

38
00:01:23,320 --> 00:01:24,160
Not at all.
完全不是。

39
00:01:24,200 --> 00:01:27,000
This is a breakthrough with real potential
这是一项具有真正潜力的突破。

40
00:01:27,000 --> 00:01:29,400
for medicine, biotech,
对医学和生物技术领域。

41
00:01:29,400 --> 00:01:31,480
maybe even greener industries down the line.
或许未来还会有更环保的产业。

42
00:01:31,480 --> 00:01:34,400
Okay, so we're talking tiny molecular changes.
好的，我们讨论的是微小的分子变化。

43
00:01:34,400 --> 00:01:35,840
Huge impacts.
巨大的影响。

44
00:01:35,840 --> 00:01:38,360
Before we jump into the solution, DP-STAB,
在我们深入解决方案DP-STAB之前，

45
00:01:38,360 --> 00:01:41,040
let's really focus on the problem itself.
让我们先聚焦问题本身。

46
00:01:41,040 --> 00:01:43,360
Why has this been so difficult,
为什么预测这些稳定性变化，

47
00:01:43,360 --> 00:01:45,000
predicting these stability changes?
如此困难？

48
00:01:45,000 --> 00:01:47,160
Yeah, the ophegie and dectorium values.
是的，就是奥菲吉和德克托里姆值。

49
00:01:47,160 --> 00:01:50,120
Right, titrine is the change in Gibbs free energy,
对，蒂特林是吉布斯自由能的变化，

50
00:01:50,120 --> 00:01:51,840
sort of a measure of stability change.
算是衡量稳定性变化的指标。

51
00:01:51,880 --> 00:01:55,040
And DP team is the melting temperature change.
而DP团队研究的是熔解温度变化。

52
00:01:55,040 --> 00:01:55,880
Knowing these.
了解这些。

53
00:01:55,880 --> 00:01:57,680
It's gold dust for protein engineering, right?
对蛋白质工程来说就是黄金，对吧？

54
00:01:57,680 --> 00:01:58,640
Or designing new drugs.
或者设计新药物时。

55
00:01:58,640 --> 00:01:59,680
Absolutely invaluable.
绝对是无价之宝。

56
00:01:59,680 --> 00:02:01,280
If you wanna make a protein more stable
如果你想提高蛋白质稳定性，

57
00:02:01,280 --> 00:02:03,480
or design a drug that interacts correctly,
或设计正确相互作用的药物，

58
00:02:03,480 --> 00:02:04,600
you need to predict this stuff.
就需要预测这些数据。

59
00:02:04,600 --> 00:02:06,960
But yeah, accuracy has been a massive hurdle.
但准确度一直是个巨大障碍。

60
00:02:06,960 --> 00:02:08,960
So what were the old ways of trying?
过去尝试过哪些方法？

61
00:02:08,960 --> 00:02:12,040
Well, historically, there were two main paths.
从历史上看，主要有两种方法。

62
00:02:12,040 --> 00:02:14,840
First, you had structure-based methods.
第一种是基于结构的方法。

63
00:02:14,840 --> 00:02:15,680
Okay, it makes sense.
好的，这很合理。

64
00:02:15,680 --> 00:02:16,760
You look at the 3D structure.
你需要观察三维结构。

65
00:02:16,760 --> 00:02:17,600
Exactly.
没错。

66
00:02:17,600 --> 00:02:21,720
You need a high quality, detailed 3D map of the protein,
你需要一份高质量、详细的蛋白质三维图谱，

67
00:02:21,720 --> 00:02:25,200
like needing the exact blueprint of an engine
就像需要发动机的精确蓝图，

68
00:02:25,200 --> 00:02:28,240
to see how changing one tiny part affects it.
才能看出改变一个小零件会有什么影响。

69
00:02:28,240 --> 00:02:29,080
Makes sense.
有道理。

70
00:02:29,080 --> 00:02:29,920
What's the catch?
难点在哪里？

71
00:02:29,920 --> 00:02:31,840
The catch is getting that blueprint.
难点在于获取那份蓝图。

72
00:02:31,840 --> 00:02:34,320
High quality structures are often really hard,
高质量的结构通常非常困难，

73
00:02:34,320 --> 00:02:36,440
sometimes impossible to get.
有时甚至无法获得。

74
00:02:36,440 --> 00:02:38,840
So these methods just weren't applicable
因此在很多情况下，

75
00:02:38,840 --> 00:02:40,160
in many, many cases.
这些方法根本不适用。

76
00:02:40,160 --> 00:02:42,080
No structure, no prediction.
没有结构，就无法预测。

77
00:02:42,080 --> 00:02:43,080
Okay, so that's limited.
好的，所以这很有局限性。

78
00:02:43,080 --> 00:02:44,040
What was the alternative?
那替代方案是什么？

79
00:02:44,040 --> 00:02:47,000
The other main approach was sequence-based methods.
另一种主要方法是基于序列的方法。

80
00:02:47,000 --> 00:02:49,560
These are way more appealing for large-scale stuff
这些方法在大规模应用中更具吸引力。

81
00:02:49,560 --> 00:02:52,680
because you only need the protein's amino acid sequence.
因为你只需要蛋白质的氨基酸序列。

82
00:02:52,680 --> 00:02:55,240
Which is just the string of letters, the basic code.
那不过是一串字母，基础代码而已。

83
00:02:55,240 --> 00:02:57,720
Right, and we have tons of sequence data.
对，我们有海量的序列数据。

84
00:02:57,720 --> 00:02:58,920
Much easier to get.
获取起来容易得多。

85
00:02:58,920 --> 00:02:59,760
But they struggled.
但他们当时举步维艰。

86
00:02:59,760 --> 00:03:00,600
Why was that?
这是为什么？

87
00:03:00,600 --> 00:03:01,560
They struggled, yeah.
确实很艰难。

88
00:03:01,560 --> 00:03:04,480
Because fundamentally, they couldn't really see
因为从根本上说，他们无法真正观察到

89
00:03:04,480 --> 00:03:06,880
the structural changes happening locally
局部发生的结构变化

90
00:03:06,880 --> 00:03:08,200
right around the mutation site.
就在突变位点周围。

91
00:03:08,200 --> 00:03:09,040
Ah, okay.
啊，明白了。

92
00:03:09,040 --> 00:03:10,760
So even if the whole protein
所以即使整个蛋白质

93
00:03:10,760 --> 00:03:12,520
didn't change shape drastically.
没有发生剧烈形变。

94
00:03:12,520 --> 00:03:13,560
Exactly.
正是。

95
00:03:13,560 --> 00:03:17,200
Those subtle shifts in how atoms interact nearby,
那些原子在附近相互作用的微妙变化，

96
00:03:17,200 --> 00:03:19,400
those are critical for stability.
对稳定性至关重要。

97
00:03:19,400 --> 00:03:21,120
And sequence methods, well,
而序列分析方法

98
00:03:21,120 --> 00:03:23,480
they just couldn't capture that nuance effectively.
根本无法有效捕捉这种细微差别。

99
00:03:23,480 --> 00:03:25,960
They were missing that local structural context.
他们缺失了局部结构信息。

100
00:03:25,960 --> 00:03:27,360
And there was another problem too, wasn't there?
其实还有另一个问题，对吧？

101
00:03:27,360 --> 00:03:29,080
Something about the data itself.
关于数据本身的一些情况。

102
00:03:29,080 --> 00:03:31,640
Oh yeah, a big one.
哦对，一个很大的问题。

103
00:03:31,640 --> 00:03:32,640
Data imbalance.
数据不平衡。

104
00:03:32,640 --> 00:03:33,480
Meaning.
意思是。

105
00:03:33,480 --> 00:03:36,400
Meaning that when you look at the experimental data we have,
意思是当你查看我们的实验数据时，

106
00:03:36,400 --> 00:03:39,600
mutations that destabilize a protein
导致蛋白质不稳定的突变

107
00:03:39,600 --> 00:03:41,680
are far, far more common
远远更为常见

108
00:03:41,680 --> 00:03:43,680
than mutations that make it more stable.
比那些使其更稳定的突变。

109
00:03:43,680 --> 00:03:45,720
Which is often what you want for engineering, right?
而这通常是工程中需要的，对吧？

110
00:03:45,720 --> 00:03:46,800
A more stable protein.
更稳定的蛋白质。

111
00:03:46,800 --> 00:03:47,640
Precisely.
正是。

112
00:03:47,640 --> 00:03:50,360
So the data sets used to train prediction models
所以用于训练预测模型的数据集

113
00:03:50,360 --> 00:03:53,200
are heavily skewed towards destabilizing examples.
严重偏向于不稳定的例子。

114
00:03:53,200 --> 00:03:54,560
Which can bias the models.
这会使模型产生偏差。

115
00:03:54,560 --> 00:03:55,400
Yeah.
是的。

116
00:03:55,400 --> 00:03:57,240
Make them less likely to find those rare,
使它们更难发现那些罕见的、

117
00:03:57,240 --> 00:03:58,840
valuable, stabilizing ones.
有价值的、稳定的突变。

118
00:03:58,840 --> 00:03:59,800
Exactly.
没错。

119
00:03:59,800 --> 00:04:02,160
The models might just learn to predict destabilizing
模型可能只学会在大多数情况下预测不稳定的情况，

120
00:04:02,160 --> 00:04:03,000
most of the time,
大多数时候，

121
00:04:03,000 --> 00:04:05,160
because that's what they see most often in the training data.
因为这是他们在训练数据中最常看到的内容。

122
00:04:05,160 --> 00:04:07,040
Okay, so we've got missing structures,
好的，我们现在面临缺失的结构、

123
00:04:07,040 --> 00:04:09,400
methods that can't see local changes,
无法察觉局部变化的方法、

124
00:04:09,400 --> 00:04:10,640
and biased data.
以及有偏差的数据。

125
00:04:10,640 --> 00:04:12,120
That's quite a mountain to climb.
这真是座难以攀登的高山。

126
00:04:12,120 --> 00:04:15,640
It really sets the stage for why a new approach was needed.
这确实说明了为何需要一种新方法。

127
00:04:15,640 --> 00:04:17,440
All right, so enter DP step.
好了，现在进入DP步骤。

128
00:04:17,440 --> 00:04:19,000
This is where it gets really interesting.
从这里开始变得真正有趣。

129
00:04:19,000 --> 00:04:23,760
The paper proposes this as a novel,
该论文将其作为一种新颖的、

130
00:04:23,760 --> 00:04:26,280
sequence-based deep learning solution.
基于序列的深度学习解决方案提出。

131
00:04:26,280 --> 00:04:28,640
Designed specifically to tackle all those limitations
专门设计用于解决我们刚刚列出的

132
00:04:28,640 --> 00:04:29,480
we just laid out.
所有那些局限性。

133
00:04:29,480 --> 00:04:30,320
That's the goal.
这就是目标。

134
00:04:30,320 --> 00:04:31,960
To get the kind of insight you'd expect
获得你期望从基于结构的方法中

135
00:04:31,960 --> 00:04:33,600
from structure-based methods,
得到的那种洞见，

136
00:04:33,600 --> 00:04:35,240
but using only the sequence,
但仅使用序列，

137
00:04:35,240 --> 00:04:36,800
making it much more scalable.
使其更具可扩展性。

138
00:04:36,800 --> 00:04:37,800
So how does it do that?
那么它是如何做到的呢？

139
00:04:37,800 --> 00:04:39,120
What's the core idea?
核心思想是什么？

140
00:04:39,120 --> 00:04:42,040
The core innovation is using a very powerful,
核心创新在于使用了一种非常强大的、

141
00:04:42,040 --> 00:04:45,480
protein-large language model, a PLLM.
蛋白质大语言模型，简称PLLM。

142
00:04:45,480 --> 00:04:48,600
Like the AI models that write text, but for protein.
类似生成文本的AI模型，但针对蛋白质。

143
00:04:48,600 --> 00:04:49,560
Exactly like that.
正是如此。

144
00:04:49,560 --> 00:04:51,680
It's trained on truly massive data sets
它基于海量数据集训练

145
00:04:51,680 --> 00:04:52,800
of protein sequences.
蛋白质序列数据。

146
00:04:52,800 --> 00:04:54,720
It learns the language of proteins,
它学习蛋白质的语言，

147
00:04:54,720 --> 00:04:57,120
how they evolve, how residues tend to interact.
它们的进化方式及残基相互作用规律。

148
00:04:57,120 --> 00:05:00,440
Specifically, DP step uses a model called ESM-2.
具体而言，DP步骤使用名为ESM-2的模型。

149
00:05:00,440 --> 00:05:02,280
ESM-2, okay, I've heard of that one.
ESM-2，我听说过这个。

150
00:05:02,280 --> 00:05:03,720
Yeah, it's state-of-the-art.
是的，这是最先进的模型。

151
00:05:03,720 --> 00:05:06,720
So ESM-2 helps DP step understand evolutionary patterns,
ESM-2帮助DP步骤理解进化模式，

152
00:05:06,720 --> 00:05:08,400
but here's the clever bit.
但精妙之处在于。

153
00:05:08,400 --> 00:05:10,680
It can also infer conformational information
它还能从序列中推断

154
00:05:10,680 --> 00:05:11,520
from the sequence.
构象信息。

155
00:05:11,520 --> 00:05:13,920
Confirmational, like shape information.
构象信息，比如形状信息。

156
00:05:13,920 --> 00:05:17,160
Sort of, it can predict things like residue contact maps.
某种程度上，它能预测残基接触图等。

157
00:05:17,160 --> 00:05:18,960
Basically, which amino acids are likely
本质上就是预测哪些氨基酸

158
00:05:18,960 --> 00:05:20,560
to be physically close to each other
在折叠的3D结构中

159
00:05:20,560 --> 00:05:22,440
in the folded 3D structure?
可能物理距离接近。

160
00:05:22,440 --> 00:05:25,120
Ah, so it's predicting structural features
啊，所以它是在预测结构特征。

161
00:05:25,120 --> 00:05:27,720
without needing the actual experimental structure.
无需实际实验结构

162
00:05:27,720 --> 00:05:31,120
Exactly, it's like inferring the shape of a complex,
没错，就像通过推断来还原复杂形状

163
00:05:31,120 --> 00:05:33,600
not just by looking at the sequence of the string.
而不仅仅是观察字符串序列

164
00:05:33,600 --> 00:05:35,760
It gives it that missing structural context.
它提供了缺失的结构上下文

165
00:05:35,760 --> 00:05:38,240
Wow, okay, that's clever.
哇，这很巧妙

166
00:05:38,240 --> 00:05:40,120
But you mentioned some secret sauce too,
但你提到还有些"秘制酱料"

167
00:05:40,120 --> 00:05:41,360
specific strategies.
特定策略

168
00:05:41,360 --> 00:05:42,900
Right, there are a couple of key things
对，有几个关键要素

169
00:05:42,900 --> 00:05:44,860
that make DP-STUG really stand out.
让DP-STUG真正脱颖而出

170
00:05:44,860 --> 00:05:49,060
First is this self-distillation inference strategy.
首先是这种自蒸馏推理策略

171
00:05:49,060 --> 00:05:51,740
Self-distillation, what does that mean in practice?
自蒸馏，实际是指什么？

172
00:05:51,740 --> 00:05:54,300
It sounds complicated, but it's a smart way
听起来复杂，其实是模型自我提升的聪明方法

173
00:05:54,300 --> 00:05:56,340
for the model to teach itself and improve.
让模型自我教学并改进

174
00:05:56,340 --> 00:06:00,940
Imagine the model makes predictions on some data,
想象模型对某些数据做出预测

175
00:06:00,940 --> 00:06:02,860
then it uses those own predictions
然后把这些预测结果

176
00:06:02,860 --> 00:06:04,580
as additional training examples,
作为额外的训练样本

177
00:06:04,580 --> 00:06:06,100
but only the confident ones.
但只采用高置信度的预测

178
00:06:06,100 --> 00:06:08,580
So it refines its knowledge using its own output?
所以它用自己的输出来完善知识？

179
00:06:08,580 --> 00:06:10,340
Pretty much, it enriches the training data,
基本如此，这丰富了训练数据

180
00:06:10,340 --> 00:06:12,620
helps the model learn more robust patterns,
帮助模型学习更稳健的模式

181
00:06:12,620 --> 00:06:14,380
and makes it better at generalizing,
并提升其泛化能力，

182
00:06:14,380 --> 00:06:16,060
applying its knowledge to new proteins
将所学知识应用于新蛋白质

183
00:06:16,060 --> 00:06:17,140
that hasn't seen before.
那些从未见过的蛋白质。

184
00:06:17,140 --> 00:06:18,700
Okay, that's one part, what was the other?
好，这是一部分，另一部分呢？

185
00:06:18,700 --> 00:06:21,180
The other key strategy is the anti-symmetric constraint.
另一个关键策略是反对称约束。

186
00:06:21,180 --> 00:06:22,900
Anti-symmetric, okay, break that down.
反对称，好，具体解释一下。

187
00:06:22,900 --> 00:06:25,460
It boils down to basic logic.
这归结为基本逻辑。

188
00:06:25,460 --> 00:06:28,380
If you mutate an amino acid, say from A to B,
如果你突变一个氨基酸，比如从A到B，

189
00:06:28,380 --> 00:06:31,380
and that changes the stability by a certain amount,
这会以一定量改变稳定性，

190
00:06:31,380 --> 00:06:33,380
let's say it decreases it by two units.
假设它降低了两个单位。

191
00:06:33,380 --> 00:06:34,220
Okay.
好。

192
00:06:34,220 --> 00:06:36,500
Then logically, mutating it back from B to A
那么逻辑上，将它从B突变回A

193
00:06:36,500 --> 00:06:38,020
should do the exact opposite, right?
应该产生完全相反的效果，对吧？

194
00:06:38,020 --> 00:06:39,860
It should increase stability by two units.
它应该将稳定性提高两个单位。

195
00:06:39,860 --> 00:06:41,140
Makes perfect sense.
完全合理。

196
00:06:41,180 --> 00:06:42,900
DGPG should just flip its sign.
DGPG应该只是翻转其符号。

197
00:06:42,900 --> 00:06:43,740
Exactly.
正是。

198
00:06:43,740 --> 00:06:47,100
DGB should equal negative DBA.
DGB应等于负DBA。

199
00:06:47,100 --> 00:06:49,380
But older models didn't always follow this rule.
但旧模型并不总是遵循这一规则。

200
00:06:49,380 --> 00:06:50,500
Really?
真的吗？

201
00:06:50,500 --> 00:06:52,180
That seems like a fundamental property.
这似乎是一个基本属性。

202
00:06:52,180 --> 00:06:55,380
It is, but their architecture didn't enforce it.
确实是，但他们的架构没有强制执行这一点。

203
00:06:55,380 --> 00:06:58,460
DPSTAP explicitly builds in this constraint.
DPSTAP明确构建了这一约束。

204
00:06:58,460 --> 00:07:00,740
It forces the model to learn and respect
它迫使模型学习并遵守

205
00:07:00,740 --> 00:07:04,380
this fundamental symmetry, or rather, anti-symmetry.
这种基本对称性，或者说反对称性。

206
00:07:04,380 --> 00:07:07,220
Which makes the predictions more physically realistic
这使得预测在物理上更真实

207
00:07:07,220 --> 00:07:08,060
and reliable.
且可靠。

208
00:07:08,060 --> 00:07:08,900
Much more robust, yes.
更加稳健，没错。

209
00:07:08,900 --> 00:07:10,780
It ensures that internal consistency.
它确保了内部一致性。

210
00:07:10,780 --> 00:07:13,020
Okay, so we have the PLMM for context,
好的，我们有PLMM作为上下文模型，

211
00:07:13,020 --> 00:07:14,980
self-distillation for better learning,
自蒸馏用于更好的学习，

212
00:07:14,980 --> 00:07:16,620
and this anti-symmetric constraint
以及这种反对称约束

213
00:07:16,620 --> 00:07:18,260
for logical consistency.
来保证逻辑一致性。

214
00:07:18,260 --> 00:07:19,420
Can we peek into the hood a bit?
我们能稍微深入了解一下吗？

215
00:07:19,420 --> 00:07:20,940
How are these pieces put together?
这些部分是如何组合在一起的？

216
00:07:20,940 --> 00:07:21,780
Sure.
当然。

217
00:07:21,780 --> 00:07:24,740
DPSTAP basically has three main modules working together.
DPSTAP基本上有三个主要模块协同工作。

218
00:07:24,740 --> 00:07:26,620
First, the sequential encoder.
首先是序列编码器。

219
00:07:26,620 --> 00:07:27,900
That's the ESM2 part.
那是ESM2部分。

220
00:07:27,900 --> 00:07:29,940
Yep, powered by ESM2.
对，由ESM2驱动。

221
00:07:29,940 --> 00:07:32,500
Its job is to take the protein sequence
它的任务是获取蛋白质序列

222
00:07:32,500 --> 00:07:35,820
and extract all that rich evolutionary information,
并提取所有丰富的进化信息

223
00:07:35,820 --> 00:07:37,820
and also predict those contact maps.
同时预测那些接触图谱

224
00:07:37,820 --> 00:07:40,100
We talked about the inferred structural information.
我们讨论过推断的结构信息

225
00:07:40,100 --> 00:07:42,140
It's the initial processing unit.
这是初始处理单元

226
00:07:42,140 --> 00:07:43,180
Got it. What's next?
明白了。接下来是什么？

227
00:07:43,180 --> 00:07:44,940
Next comes the neighboring encoder.
接下来是相邻编码器

228
00:07:44,940 --> 00:07:47,620
This is where it really zooms in on the mutation site.
这里会真正聚焦突变位点

229
00:07:47,620 --> 00:07:49,220
Focusing on the local environment.
专注于局部环境

230
00:07:49,220 --> 00:07:50,260
Exactly.
正是

231
00:07:50,260 --> 00:07:53,580
It uses something called a cross-attention mechanism.
它使用一种交叉注意力机制

232
00:07:53,580 --> 00:07:55,820
Think of it like giving the model the ability
可以理解为赋予模型能力

233
00:07:55,820 --> 00:07:58,260
to pay special attention to the mutated residue
使其特别关注突变残基

234
00:07:58,260 --> 00:07:59,780
and its important neighbors.
及其重要相邻残基

235
00:07:59,780 --> 00:08:02,940
It figures out which nearby residues are most relevant.
它能识别哪些邻近残基最相关

236
00:08:02,940 --> 00:08:04,580
And it integrates information from them.
并整合来自它们的信息

237
00:08:04,580 --> 00:08:05,860
Yes, and critically,
是的，关键是

238
00:08:05,860 --> 00:08:08,780
it can also capture long-range interactions.
它还能捕捉长程相互作用

239
00:08:08,780 --> 00:08:10,900
Sometimes residues far apart in the sequence
有时序列上相距较远的残基

240
00:08:10,900 --> 00:08:13,980
actually fold together and interact in 3D space.
实际上会在三维空间中折叠并相互作用

241
00:08:13,980 --> 00:08:16,220
This module helps capture those important,
该模块有助于捕捉那些重要、

242
00:08:16,220 --> 00:08:19,380
non-local effects that simpler methods often miss.
简单方法常忽略的非局部效应。

243
00:08:19,380 --> 00:08:20,900
Okay, so sequence info,
好的，首先是序列信息，

244
00:08:20,900 --> 00:08:23,620
then focused local and long-range info.
然后是聚焦的局部和长程信息。

245
00:08:23,620 --> 00:08:24,620
What's the final step?
最后一步是什么？

246
00:08:24,620 --> 00:08:27,180
The final piece is the feature transition module.
最后环节是特征转换模块。

247
00:08:27,180 --> 00:08:29,380
This module takes all the information gathered
该模块整合所有收集到的

248
00:08:29,380 --> 00:08:31,820
about the original protein, the wild type,
关于原始蛋白质（野生型）

249
00:08:31,820 --> 00:08:33,060
and the mutated version.
和突变版本的信息。

250
00:08:33,060 --> 00:08:33,900
Just binds them.
直接将其绑定。

251
00:08:33,900 --> 00:08:35,860
Right, it integrates features from both,
对，它融合了双方特征，

252
00:08:35,860 --> 00:08:37,380
and importantly, it also factors in
关键是还纳入了

253
00:08:37,380 --> 00:08:39,860
environmental conditions like pH and temperature,
pH值和温度等环境条件，

254
00:08:39,860 --> 00:08:41,620
especially for the age predictions,
特别是对于老化预测，

255
00:08:41,620 --> 00:08:44,020
because stability can depend on those factors too.
因为稳定性也受这些因素影响。

256
00:08:44,020 --> 00:08:46,260
And then it spits out the final prediction,
最后输出最终预测结果，

257
00:08:46,260 --> 00:08:48,580
the AG or aid value.
即AG值或aid值。

258
00:08:48,580 --> 00:08:50,980
Precisely, it synthesizes everything
准确地说，它综合所有数据

259
00:08:50,980 --> 00:08:53,700
to make that final call on the stability change.
做出稳定性变化的最终判断。

260
00:08:53,700 --> 00:08:55,820
Okay, that sounds incredibly sophisticated.
好的，听起来极其精密。

261
00:08:55,820 --> 00:08:57,540
So the million dollar question,
所以关键问题是，

262
00:08:58,380 --> 00:09:00,460
how well does it actually work?
它实际效果如何？

263
00:09:00,460 --> 00:09:02,100
Did all this complexity pay off?
这些复杂设计是否值得？

264
00:09:02,100 --> 00:09:02,940
Oh, absolutely.
噢，绝对值得。

265
00:09:02,940 --> 00:09:05,900
The results reported are really quite striking.
报告的结果确实非常惊人。

266
00:09:05,900 --> 00:09:08,940
DP-STAB seems to be setting a new state of the art.
DP-STAB似乎创造了新标杆。

267
00:09:08,940 --> 00:09:09,780
Across the board.
全面领先。

268
00:09:09,780 --> 00:09:10,700
Pretty much, yeah.
基本是这样，没错。

269
00:09:10,700 --> 00:09:12,180
Let's talk AG prediction first,
我们先说AG预测，

270
00:09:12,180 --> 00:09:13,580
that energy change measure.
那个能量变化指标。

271
00:09:13,580 --> 00:09:16,500
DP-STAB significantly lowers the prediction errors
相比之前的方法，

272
00:09:16,500 --> 00:09:18,100
compared to previous methods.
DP-STAB显著降低了预测误差。

273
00:09:18,100 --> 00:09:20,420
Lower errors meaning more accurate.
误差更低意味着更准确。

274
00:09:20,420 --> 00:09:21,340
Much more accurate.
准确得多。

275
00:09:21,340 --> 00:09:24,620
The RMSC, which is a kind of average error,
RMSC作为平均误差指标，

276
00:09:24,620 --> 00:09:27,740
dropped below 0.93 kilocal.
已降至0.93千卡以下。

277
00:09:27,740 --> 00:09:31,860
The MAE, another error measure, is below 0.68 kilomole.
另一误差指标MAE低于0.68千摩尔。

278
00:09:31,860 --> 00:09:33,180
Lower is better here.
数值越低越好。

279
00:09:33,180 --> 00:09:34,660
And how well did the predictions match
预测结果与实验室实测

280
00:09:34,660 --> 00:09:36,380
the actual lab experiments?
吻合度如何？

281
00:09:36,380 --> 00:09:37,220
Very well.
很好。

282
00:09:37,220 --> 00:09:38,700
The correlation coefficients,
相关系数，

283
00:09:38,700 --> 00:09:41,700
which measure how well predictions line up with reality,
用于衡量预测与现实的吻合程度，

284
00:09:41,700 --> 00:09:42,820
are really high.
数值确实很高。

285
00:09:42,820 --> 00:09:45,780
SEC, the Spearman correlation hit 0.86,
SEC（斯皮尔曼相关系数）达到0.86，

286
00:09:45,780 --> 00:09:49,900
and PCC, the Pearson correlation reached 0.84.
PCC（皮尔逊相关系数）则为0.84。

287
00:09:49,900 --> 00:09:52,260
Closer to one is better, so those are excellent scores.
越接近1表示越好，这些分数非常出色。

288
00:09:52,260 --> 00:09:55,020
Wow, okay, that's impressive on the continuous values.
哇，连续值上的表现令人印象深刻。

289
00:09:55,020 --> 00:09:57,100
What about just classifying mutations?
仅对突变进行分类呢？

290
00:09:57,100 --> 00:10:01,020
Like is it stabilizing, destabilizing, or neutral?
比如判断它是稳定化、去稳定化还是中性？

291
00:10:01,020 --> 00:10:02,620
That's often the practical question
这通常是实际应用中

292
00:10:02,620 --> 00:10:03,780
you want answered quickly.
需要快速解答的问题。

293
00:10:03,780 --> 00:10:06,700
And here too, DP-STAB performs exceptionally well.
在这方面DP-STAB表现同样卓越。

294
00:10:06,700 --> 00:10:09,900
Its classification accuracy is over 74%.
其分类准确率超过74%。

295
00:10:09,900 --> 00:10:11,620
So roughly three out of four times,
大约每四次判断中就有三次

296
00:10:11,620 --> 00:10:14,500
it correctly flags whether a mutation will strengthen,
能正确识别突变会增强、

297
00:10:14,500 --> 00:10:16,620
weaken, or not affect the protein.
削弱还是不影响蛋白质。

298
00:10:16,620 --> 00:10:17,460
That's right.
没错。

299
00:10:17,460 --> 00:10:21,900
In their tests, it correctly classified 684 out of 922 cases.
在测试中，它正确分类了922个案例中的684个。

300
00:10:21,900 --> 00:10:24,420
That's a huge improvement, and really useful
这是重大改进，且极具实用价值。

301
00:10:24,420 --> 00:10:27,300
for screening potential mutations in drug design
用于药物设计中潜在突变的筛选

302
00:10:27,300 --> 00:10:28,700
or enzyme engineering.
或酶工程领域。

303
00:10:28,700 --> 00:10:31,100
Cuts down the lab work significantly, I imagine.
我认为这能大幅减少实验室工作量。

304
00:10:31,100 --> 00:10:33,340
Dramatically, saves time and resources.
显著节省时间和资源。

305
00:10:33,340 --> 00:10:35,060
And you mentioned team prediction too,
你还提到了团队预测，

306
00:10:35,060 --> 00:10:36,180
the melting temperature.
即熔解温度。

307
00:10:36,180 --> 00:10:39,820
Yes, performance there was also reported as strong
是的，该方面的性能也被报道为很强

308
00:10:39,820 --> 00:10:42,340
or at least comparable to the best existing methods.
或至少可与现有最佳方法相媲美。

309
00:10:42,340 --> 00:10:44,180
So it's delivering on multiple fronts.
因此它在多个方面都表现出色。

310
00:10:44,180 --> 00:10:46,340
Can we see it in action, like specific examples?
我们能看具体实例演示吗？

311
00:10:46,340 --> 00:10:49,140
Yeah, the paper highlights how well DP-STAB performs
论文重点展示了DP-STAB在

312
00:10:49,140 --> 00:10:51,540
on individual proteins, which is crucial
单个蛋白质上的优异表现，

313
00:10:51,540 --> 00:10:53,580
for practical engineering tasks.
这对实际工程任务至关重要。

314
00:10:53,580 --> 00:10:56,340
Compared to older methods, they saw median improvements
与传统方法相比，其中位数提升表现为：

315
00:10:56,340 --> 00:11:00,260
of like 9% in SEC, 7.5% in PCC,
SEC指标提升9%，PCC提升7.5%，

316
00:11:00,300 --> 00:11:03,420
and almost 20% in classification accuracy per protein.
单蛋白分类准确率提升近20%。

317
00:11:03,420 --> 00:11:04,900
So it's not just good on average.
所以它不仅在平均水平上优秀。

318
00:11:04,900 --> 00:11:07,700
It's reliably better for specific cases you might care about.
在您关心的具体案例中也稳定更优。

319
00:11:07,700 --> 00:11:08,620
Exactly.
确实如此。

320
00:11:08,620 --> 00:11:11,540
They showed a case study, Protein 2-PTLA,
他们展示了一个案例研究——2-PTLA蛋白，

321
00:11:11,540 --> 00:11:13,980
with an A34G mutation.
带有A34G突变

322
00:11:13,980 --> 00:11:16,300
DP-STAB predicted a stability decrease,
DP-STAB预测稳定性下降

323
00:11:16,300 --> 00:11:18,860
a DG of Monash 1.2 kilomole.
单次变化自由能为1.2千摩尔

324
00:11:18,860 --> 00:11:19,700
And the real value?
实际值是多少？

325
00:11:19,700 --> 00:11:22,260
The experimental value was 9.2.1 kilomole,
实验值为9.2千摩尔

326
00:11:22,260 --> 00:11:23,380
so quite close.
相当接近

327
00:11:23,380 --> 00:11:25,380
But importantly, a previous leading method
但重要的是，先前的主流方法

328
00:11:25,380 --> 00:11:26,780
predicted it as neutral,
预测结果为中性

329
00:11:26,780 --> 00:11:28,780
completely missing the destabilization.
完全忽略了失稳效应

330
00:11:28,780 --> 00:11:30,460
Why did DP-STAB get it right?
为什么DP-STAB能准确预测？

331
00:11:30,460 --> 00:11:31,980
Did it offer an explanation?
它提供解释了吗？

332
00:11:31,980 --> 00:11:33,340
Yes, that's the beauty.
是的，这正是其优势

333
00:11:33,340 --> 00:11:35,580
The analysis suggested that replacing alanine A
分析表明用更小的甘氨酸G替代丙氨酸A

334
00:11:35,580 --> 00:11:38,500
with a smaller glycine G led to a loss of key interactions
导致与Y50和F36等邻近残基的关键相互作用丧失

335
00:11:38,500 --> 00:11:41,540
with nearby residues, like Y50 and F36.
这破坏了关键相互作用

336
00:11:41,540 --> 00:11:43,380
It pinpointed the structural reason.
它精准指出了结构原因

337
00:11:43,380 --> 00:11:45,260
Ah, providing insight, not just a number.
啊，提供了洞见而不仅是数字

338
00:11:45,260 --> 00:11:46,100
Precisely.
正是如此

339
00:11:46,100 --> 00:11:49,500
Another example, Protein 106XA, mutation H51A.
另一个例子：蛋白质106XA的H51A突变

340
00:11:49,500 --> 00:11:52,220
DP-STAB predicted managed 0.8 kilomole.
DP-STAB预测值为0.8千摩尔

341
00:11:52,220 --> 00:11:54,180
The actual was managed 0.7 kilomole.
实际管理量为0.7千摩尔。

342
00:11:54,180 --> 00:11:57,540
Again, very close, and the old methods.
再次验证，结果非常接近，采用传统方法。

343
00:11:57,540 --> 00:12:01,020
A prior method incorrectly called it stable.
先前方法错误地判定其为稳定状态。

344
00:12:01,020 --> 00:12:03,340
DP-STAB's analysis pointed to the loss
DP-STAB的分析指出关键氢键的丢失

345
00:12:03,340 --> 00:12:04,900
of important hydrogen bonds
重要氢键的缺失

346
00:12:04,900 --> 00:12:07,220
caused by removing the histidine H.
由组氨酸H的移除导致

347
00:12:07,220 --> 00:12:10,700
It's seeing those crucial atomic level interactions.
它能识别这些关键的原子级相互作用

348
00:12:10,700 --> 00:12:13,180
It seems to be capturing them much better, yes.
确实，它似乎能更精准地捕捉这些细节

349
00:12:13,180 --> 00:12:14,940
These specific accurate insights
这些精确的特定洞察

350
00:12:14,940 --> 00:12:17,100
are game changers for protein engineers.
对蛋白质工程师具有颠覆性意义

351
00:12:17,100 --> 00:12:18,500
What about scalability?
可扩展性如何？

352
00:12:18,500 --> 00:12:20,460
Can it handle really big data sets?
能否处理真正的大规模数据集？

353
00:12:20,460 --> 00:12:22,940
You mentioned debutational scanning, DMS.
你提到了突变扫描技术DMS

354
00:12:22,940 --> 00:12:26,100
Yeah, DMS experiments generate data on thousands,
是的，DMS实验能生成单蛋白数千

355
00:12:26,100 --> 00:12:29,620
even tens of thousands, of mutations for a single protein.
甚至数万种突变的数据

356
00:12:29,620 --> 00:12:31,780
It's a massive amount of information.
这是海量的信息

357
00:12:31,780 --> 00:12:32,780
A DP-STAB.
而DP-STAB

358
00:12:32,780 --> 00:12:34,900
It performed very well there, too.
在此方面同样表现优异

359
00:12:34,900 --> 00:12:37,500
On data sets with around 20,000 mutations,
在约20,000个突变的数据集上

360
00:12:37,500 --> 00:12:39,620
DP-STAB showed significant improvements
DP-STAB展现出显著改进

361
00:12:39,620 --> 00:12:40,980
in correlation metrics.
在相关性指标中。

362
00:12:40,980 --> 00:12:45,660
SCC boosted by 13%, Kendall correlation by 15%.
SCC提升了13%，肯德尔相关系数提升了15%。

363
00:12:45,660 --> 00:12:46,500
It appears so.
看起来是这样。

364
00:12:46,500 --> 00:12:49,060
It's efficient enough for these large-scale analyses,
对于这些大规模分析来说效率足够高，

365
00:12:49,060 --> 00:12:50,820
which opens up a lot of possibilities
这为更深入探索蛋白质功能景观

366
00:12:50,820 --> 00:12:53,500
for exploring protein function landscapes more deeply.
提供了许多可能性。

367
00:12:53,500 --> 00:12:54,340
It's huge.
这非常庞大。

368
00:12:54,380 --> 00:12:57,540
And there's another cool insight from the DMS data.
从DMS数据中还获得了另一个很棒的发现。

369
00:12:57,540 --> 00:13:02,060
When DP-STAB ranks mutations by predicted stability change,
当DP-STAB根据预测的稳定性变化对突变进行排序时，

370
00:13:02,060 --> 00:13:04,140
the top-ranked ones, the ones it predicts,
它预测出的那些排名靠前的突变，

371
00:13:04,140 --> 00:13:07,700
have the biggest effect, generally correspond to mutations
通常对应着真正具有较高实验DJI值的突变，

372
00:13:07,700 --> 00:13:11,100
that truly have higher experimental DJI values.
这些突变产生的影响最大。

373
00:13:11,100 --> 00:13:12,140
Meaning?
这意味着什么？

374
00:13:12,140 --> 00:13:13,700
Meaning its strongest predictions
意味着其最强的预测

375
00:13:13,700 --> 00:13:15,460
are more likely to be identifying
更有可能识别出

376
00:13:15,460 --> 00:13:18,460
those genuinely significant stabilizing mutations,
那些真正重要的稳定性突变，

377
00:13:18,460 --> 00:13:21,020
the ones researchers are often hunting for.
也就是研究人员经常寻找的突变。

378
00:13:21,020 --> 00:13:23,780
It acts like a better filter for finding the good stuff.
它就像一个更好的过滤器，能筛选出有用的东西。

379
00:13:23,780 --> 00:13:25,380
Finding the needles in the haystack.
犹如大海捞针。

380
00:13:25,380 --> 00:13:27,060
And much more effective filter, yes.
而且是一个高效得多的过滤器，确实如此。

381
00:13:27,060 --> 00:13:29,580
Okay, so the performance is clearly top-notch.
好的，性能表现显然是一流的。

382
00:13:29,580 --> 00:13:30,860
Let's circle back to why.
让我们回到原因上来。

383
00:13:30,860 --> 00:13:33,180
You mentioned those two key strategies,
你提到那两个关键策略，

384
00:13:33,180 --> 00:13:36,540
self-distillation and the anti-symmetric constraint.
自我蒸馏和反对称约束。

385
00:13:36,540 --> 00:13:37,940
How critical were they, really?
它们到底有多重要？

386
00:13:37,940 --> 00:13:40,900
They seem absolutely critical based on the ablation studies
根据消融实验来看它们绝对至关重要

387
00:13:40,900 --> 00:13:43,420
where they tested the model with those components removed.
实验中测试了移除这些组件的模型。

388
00:13:43,420 --> 00:13:45,500
What happened when they took out self-distillation?
当他们移除自我蒸馏时发生了什么？

389
00:13:45,500 --> 00:13:48,060
Performance dropped noticeably across the board.
各项性能指标都明显下降。

390
00:13:48,060 --> 00:13:50,980
For DG prediction, correlations like SCC and PCC
对于DG预测，SCC和PCC等相关系数

391
00:13:50,980 --> 00:13:52,780
went down by about 3%.
下降了约3%。

392
00:13:52,780 --> 00:13:55,220
And the errors, RMSE and MAE,
而误差指标RMSE和MAE，

393
00:13:55,220 --> 00:13:57,940
went up by 6% and 4% respectively.
分别上升了6%和4%。

394
00:13:57,940 --> 00:13:59,260
So not a minor tweak then.
看来这不是小调整。

395
00:13:59,260 --> 00:14:01,020
It really helps the model learn better.
它确实能帮助模型更好地学习。

396
00:14:01,020 --> 00:14:01,860
Definitely.
确实如此。

397
00:14:01,860 --> 00:14:03,220
It improves generalization.
它提升了泛化能力。

398
00:14:03,220 --> 00:14:05,100
They even showed it helped when testing
他们甚至证明在测试

399
00:14:05,100 --> 00:14:07,020
on completely independent data sets
完全独立的、采用不同技术生成的

400
00:14:07,020 --> 00:14:08,940
generated by different techniques,
数据集时也有效果。

401
00:14:08,940 --> 00:14:10,620
boosting performance further there too.
在那里也进一步提升性能。

402
00:14:10,620 --> 00:14:12,140
It makes the model more robust.
它使模型更加稳健。

403
00:14:12,140 --> 00:14:14,460
Okay, and the anti-symmetric constraint,
好的，还有反对称约束，

404
00:14:14,460 --> 00:14:18,180
by minus again, A to B change is the opposite of BDA.
再次减去，A到B的变化与BDA相反。

405
00:14:18,180 --> 00:14:20,820
Right, that fundamental logical consistency.
对，这是基本的逻辑一致性。

406
00:14:20,820 --> 00:14:23,620
How badly did older models violate this?
旧模型在这方面表现有多差？

407
00:14:23,620 --> 00:14:25,100
Pretty badly sometimes.
有时非常糟糕。

408
00:14:25,100 --> 00:14:26,620
Their predictions for ABMBA
它们对ABMBA的预测

409
00:14:26,620 --> 00:14:28,580
could be quite different inconsistent,
可能非常不一致，

410
00:14:28,580 --> 00:14:31,340
which is problematic if you're relying on those predictions.
如果你依赖这些预测就会有问题。

411
00:14:31,340 --> 00:14:34,380
And adding the constraint, fix this in DP-STAB.
而添加约束后，DP-STAB修复了这个问题。

412
00:14:34,380 --> 00:14:35,700
Significantly.
效果显著。

413
00:14:35,700 --> 00:14:38,060
By forcing the model to obey this rule,
通过强制模型遵守这个规则，

414
00:14:38,060 --> 00:14:39,940
DP-STAB makes the prediction errors
DP-STAB使得预测误差

415
00:14:39,940 --> 00:14:43,300
for these forward and reverse mutations much, much smaller.
在这些正向和反向突变上大幅减小。

416
00:14:43,300 --> 00:14:45,860
The outputs become almost perfectly opposite
输出结果变得几乎完全相反，

417
00:14:45,860 --> 00:14:46,940
as they should be.
正如它们应该的那样。

418
00:14:46,940 --> 00:14:48,100
Which just makes the whole system
这使得整个系统

419
00:14:48,100 --> 00:14:50,500
more trustworthy and practical for real use.
在实际应用中更值得信赖和实用。

420
00:14:50,500 --> 00:14:53,140
Exactly, you can rely on that inverse relationship
没错，你可以依赖这种反向关系。

421
00:14:53,140 --> 00:14:54,860
holding true in the predictions.
在预测中保持真实。

422
00:14:54,860 --> 00:14:56,820
It adds a layer of robustness.
它增加了一层鲁棒性。

423
00:14:56,820 --> 00:14:58,220
So let's pull this all together.
让我们把这些整合起来。

424
00:14:58,220 --> 00:14:59,980
What's the big picture here?
这里的宏观图景是什么？

425
00:14:59,980 --> 00:15:03,060
What are the main advantages DP-STAB brings to the table?
DP-STAB带来的主要优势有哪些？

426
00:15:03,060 --> 00:15:04,860
Well, first, it's sequence-based.
首先，它是基于序列的。

427
00:15:04,860 --> 00:15:07,140
That's huge, no need for those difficult
这很重要，不需要那些难以获取的

428
00:15:07,140 --> 00:15:08,500
to get 3D structures.
三维结构。

429
00:15:08,500 --> 00:15:09,740
Making it widely applicable.
使其具有广泛适用性。

430
00:15:09,740 --> 00:15:10,540
Yes.
是的。

431
00:15:10,540 --> 00:15:13,540
Second, it manages to accurately predict changes
其次，它能准确预测残基接触

432
00:15:13,540 --> 00:15:17,180
in residue contacts, essentially seeing local structural
的变化，通过PLLM仅从序列就能

433
00:15:17,180 --> 00:15:19,580
changes just from the sequence thanks to the PLLM.
看到局部结构变化。

434
00:15:19,580 --> 00:15:22,420
Getting structural insights without structures.
无需结构即可获得结构洞察。

435
00:15:22,420 --> 00:15:23,020
Right.
对。

436
00:15:23,020 --> 00:15:26,180
And third, it can pinpoint key residues involved
第三，它能精确定位涉及稳定性的

437
00:15:26,180 --> 00:15:29,220
in stability, like those forming hydrogen bonds,
关键残基，如形成氢键的残基，

438
00:15:29,220 --> 00:15:31,060
providing actionable insights.
提供可操作的见解。

439
00:15:31,060 --> 00:15:32,820
So for folks in protein engineering
对于蛋白质工程或

440
00:15:32,820 --> 00:15:34,180
or biomedical research.
生物医学研究人员来说。

441
00:15:34,180 --> 00:15:37,740
It offers a fast, practical, and highly accurate tool.
它提供了一个快速、实用且高精度的工具。

442
00:15:37,740 --> 00:15:40,620
It can accelerate the process of designing better proteins,
它能加速设计更优蛋白质的进程，

443
00:15:40,620 --> 00:15:42,500
whether for therapies, diagnostics,
无论是用于治疗、诊断，

444
00:15:42,500 --> 00:15:44,380
or industrial applications.
还是工业应用。

445
00:15:44,380 --> 00:15:46,420
Less guesswork, faster discovery.
减少猜测，加速发现。

446
00:15:46,420 --> 00:15:47,900
That sounds like a major step forward.
这听起来像是重大进步。

447
00:15:47,900 --> 00:15:48,400
Yeah.
没错。

448
00:15:48,400 --> 00:15:49,300
What's next, though?
不过接下来呢？

449
00:15:49,300 --> 00:15:51,580
What are the remaining challenges or future directions
这个领域还存在哪些挑战或未来方向？

450
00:15:51,580 --> 00:15:52,300
in this field?

451
00:15:52,300 --> 00:15:54,060
Oh, there are definitely challenges ahead.
哦，前方确实存在挑战。

452
00:15:54,060 --> 00:15:56,460
One is incorporating even finer details,
其一是纳入更精细的细节，

453
00:15:56,460 --> 00:15:59,980
like the specific orientations of amino acid side chains,
比如氨基酸侧链的具体取向，

454
00:15:59,980 --> 00:16:01,260
which can be important.
这可能很重要。

455
00:16:01,260 --> 00:16:02,620
Getting even more granular.
追求更微观的层面。

456
00:16:02,620 --> 00:16:04,620
Yeah, adding more geometric detail.
对，增加更多几何细节。

457
00:16:04,620 --> 00:16:06,100
And maybe the biggest challenge looming
而最迫切的重大挑战或许是

458
00:16:06,100 --> 00:16:08,800
is predicting the effects of multiple mutations
预测多个同时发生的突变

459
00:16:08,800 --> 00:16:10,180
happening at the same time.
所产生的影响。

460
00:16:10,180 --> 00:16:12,980
Ah, not just single changes, but combinations.
啊，不止单一变化，而是组合变化。

461
00:16:12,980 --> 00:16:13,820
Exactly.
确实。

462
00:16:13,820 --> 00:16:16,340
That adds layers and layers of complexity.
这增加了层层复杂性。

463
00:16:16,340 --> 00:16:18,100
Interactions between mutations can
突变间的相互作用

464
00:16:18,100 --> 00:16:20,060
be really tricky to predict.
可能极难预测。

465
00:16:20,060 --> 00:16:23,460
That likely requires even more powerful AI approaches.
那可能需要更强大的人工智能方法。

466
00:16:23,460 --> 00:16:24,940
Still mountains to climb, then.
仍有高山要攀登。

467
00:16:24,940 --> 00:16:26,740
Always in science.
科学永远如此。

468
00:16:26,740 --> 00:16:29,060
So wrapping up, the core takeaway
总结来说，核心要点

469
00:16:29,060 --> 00:16:32,580
seems to be that DPSTAP is a really significant advance
似乎是DPSTAP是一项重大进展

470
00:16:32,580 --> 00:16:35,620
in how we understand and engineer proteins.
在我们理解和设计蛋白质方面。

471
00:16:35,620 --> 00:16:39,060
It's a fantastic example of how sophisticated AI,
这是复杂人工智能的绝佳范例，

472
00:16:39,060 --> 00:16:40,700
like these large language models,
比如这些大型语言模型，

473
00:16:40,700 --> 00:16:43,580
can decode the complex rules of biology.
能够解码生物学的复杂规则。

474
00:16:43,580 --> 00:16:44,380
Absolutely.
完全同意。

475
00:16:44,380 --> 00:16:46,860
It reveals the hidden logic in these systems.
它揭示了这些系统中的隐藏逻辑。

476
00:16:46,900 --> 00:16:48,980
And when you think about the possibilities,
当你思考可能性时，

477
00:16:48,980 --> 00:16:51,460
being able to precisely engineer proteins
能够为特定任务精确设计蛋白质，

478
00:16:51,460 --> 00:16:54,380
for specific jobs, I mean, designing medicines
比如设计更稳定、

479
00:16:54,380 --> 00:16:56,860
that are more stable and last longer in the body,
在体内作用更持久的药物，

480
00:16:56,860 --> 00:16:58,820
creating enzymes that work more efficiently
创造效率更高的酶。

481
00:16:58,820 --> 00:17:01,020
for sustainable manufacturing,
为了可持续制造，

482
00:17:01,020 --> 00:17:02,220
it feels like we're getting closer
感觉我们正越来越接近

483
00:17:02,220 --> 00:17:04,220
to truly being able to design biology
真正能够在最基础的层面上

484
00:17:04,220 --> 00:17:06,020
at its most fundamental level.
设计生物学。

485
00:17:06,020 --> 00:17:07,780
It really opens up the imagination.
这确实激发了想象力。

486
00:17:07,780 --> 00:17:11,060
What other incredibly complex biological puzzles,
除了蛋白质稳定性，

487
00:17:11,060 --> 00:17:14,100
beyond protein stability, might we unlock next?
我们接下来还能解开哪些极其复杂的生物难题？

488
00:17:14,100 --> 00:17:15,980
Using these kinds of deep learning tools,
使用这类深度学习工具，

489
00:17:15,980 --> 00:17:17,780
what's the next big domino to fall?
下一个重大突破会是什么？

490
00:17:17,780 --> 00:17:19,740
That's the exciting question, isn't it?
这是个令人兴奋的问题，不是吗？

491
00:17:19,740 --> 00:17:21,940
The potential seems enormous.
潜力似乎无穷无尽。
