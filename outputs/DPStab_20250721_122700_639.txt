欢迎收听今天我们来深入聊聊一个生物计算理喻的新东西关于蛋白质稳定性的预测具体来说就是蛋白质里头一个安计算变了发生突变了它的稳定性会怎么变我们参考了一篇乱文介绍了一种叫DP step的新方法这次我们就想跟你一起弄明白这个DP step到底是什么它为啥重要跟现在的方法比它好在哪儿你可能会问预测稳定性它有啥用呢其实这在蛋白质工程你想想改造个没什么的还有药收集里都特别关键我们都知道预测蛋白质稳定性一直挺难的以前的方法要么就只看序列但蛋白质它是个三维的东西对吧对观看序列很难抓住结构变化带来了影响要么就得有那个精确的蛋白质三维结构是但高质量的结构数据获取起来太难了成问又高没错而且还有一个问题就是数据不平衡这个确实是个大问题就是说已知的能让蛋白质更稳定的突变例子比那种破坏稳定性的要少得多得多对这就导致模型训练的时候容易有偏见所以这就影响预测的准头了是的不过呢这个DpStab它就是冲着解决这些痛点来的它是一个基于深部学习的方法最妙的地方在于啊它主要是靠蛋白质的安基酸序列信息哦主要靠序列对它就比较巧妙地绕开了对高质量三维结构数据的那个硬性要求它用了现在挺火的那个蛋白质大语言模型具体来说是ESM2来打击处然后呢通过一个叫交叉注意力的机制特别去关注突变点周围那些安基酸的接触方式是怎么变的等一下交叉注意力这个听起来有点抽象它大概是怎么回事嗯你可以这么想就是我们理解一个词不光要看这个词本身还得看它旁边挨着的词限制整个句子的意思对吧这个比喻田形象的对这个交叉注意力机制就是让模型在分析那个突变位点的时候能自动的动态的去关注那些跟它有消货作用的或者说离得比较近的关键安基酸它会评估这些旁边的残疾因为这个突变发生了什么变化然后把这些信息综合起来判断对整体稳定性的影响哦明白了就是说更关注突变点附近的小环境变化可以这么理解然后它还有两个很关键的策略嗯一个叫自我征流自我征流自己给自己征流点什么出来哈哈有点那个意思你可以想象成模型在训练的时候啊把早期训练的还不错的自己当做一个老师嗯老师对用这个老师模型去生成一些额外的训练数据就是所谓的尾标签然后用这些数据再去知道后面阶段的训练哦这做的好处是好处就是能增加训练数据的多样性嘛让最终的模型学得更全面不容易只在某些特定数据上表现好说白了就是提高它的泛化能力让模型更见多视广那另一个策略呢另一个是反对称约数这个呃就更有意思了因为它利用了基本的物理化学原理物理化学原理是的你想啊比如一个按计算A变成B稳定性变化我们叫它delta delta g嗯那反过来如果B变回A理论上稳定性变化就应该是呃-的delta delta g对吧呃对风强相反数值一样没错这个反对称约数就是强制模型去学习这种对称性或者说反对称性这个设计非常巧妙因为它不完全是靠数据去擬合而是把物理规律可以说是硬塞给模型去遵守哇这等于是给了模型一个常识性的指导对特别是数据不太够的时候这种基于原理的约数能大大提高预测结果的可靠性让它更符合真实世界听起来理论很强啊那实际跑起来效果怎么样它真的能比那些用结构信息的方法还好吗诶这正是DP step的亮点根据论文里给的数据它在那个独立测试级上的表现确实非常亮眼哦具体说说你看啊衡量预测准确度的几个关键指标比如预测值和真实稳定性低链花值的那个相关性像PCC、SCC这些还有区分稳定不稳定图变的准确率ACC、MCCDP step都明显比好几种现有方法要强包括那些基于结构的方法对甚至包括一些需要精确结构信息的方法而且它的预测误差就是那个RMSE和MAE也更小那很厉害了嗯特别要提一下的是它在对大量突变进行排序就是判断哪个突变影响更大这方面表现特别突出排序这对什么有用这一对高通量虚险实验比如那个DMS数据验证的就非常有价值了你可以快速摔掉一大批不靠谱的突变明白了那有没有具体的例子能让我们更直观的看到它为啥预测的准路文里分析了几个案例比如说在一个叫RPTLA的蛋白质疑一个秉安酸A变成肝安酸G的突变就是A34G实验证明这个突变会降低稳定性DP step就准确预测了这一点它是怎么看出来的因为它能捕捉到这个突变导致胺基酸测链变小了结果就失去了原本和周围残疾的一些输水相互作用造成了局部的不稳定但另一个基于结构的方法叫GODDG3D反而觉得这个突变影响不大有意思还有别的例子吗还有一个是在EO6XA蛋白里的H51A突变DP step也成功预测了稳定性下降因为它识别出这个突变破坏要原有的轻贱网络所以它能抓住这些很细微的变化对这说明DP step对突变影响的理解可能更深入更能抓住导致稳定性变化的关键因素这么说来DP step的重要意义就很明显了是的它提供了一种高效 准确而且主要是基于序列的预测方法这就意味着研究人员可以更快 更广泛地去评估各种突变对蛋白质稳定性的影响不需要再等那个又慢又贵的结构测定了这对加速蛋白质设计还有煤工程要开发这些领域绝对是个巨大的推动力它克服了老方法的瓶颈好我来试着小结一下DP step的核心就是用了蛋白质大语言模型和交叉注意力再加上自我征留和反对称约束这两个独门密集让它能只靠序列就挺准的预测单个儿安计算突变对蛋白质稳定性的影响关键是又快又准适用性海广总结得很到位当然了研究肯定还在继续路门也提到了未来的风向比如说预测多个安计算同时突变的影响这个在实际应用里其实更常见但肯定也更复杂对一个变了就够麻烦了好几个一起变那相互影响就多了是啊另外呢怎么把更多细节信息比如测链的具体构向几何解构啊整合进来也是提高精度的方向所以呀这里也留给你一个问题可以思考一下从预测单个儿点突变升级到预测多个点突变你觉得这个挑战的难度会增加多少是限性的还是指数级的这会给预测模型带来哪些全新的计算上或者理论上的难题呢值得琢磨琢磨确实是个值得思考的问题好非常感谢今天的分享也感谢你的收听我们下次再会