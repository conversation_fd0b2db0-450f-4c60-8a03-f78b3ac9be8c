欢迎收听豆包AI播客节目Hello大家好欢迎收听我们这一期播客然后咱们今天来聊一聊蛋白质设计当中的人工智能和第一性原理的方法以及我们如何把这两种方法结合起来去解决一些多突变位点的这样的设计挑战听起来很有意思那我们就直接开始吧那我们就先开始第一个问题就是说目前的这些蛋白质重新设计的方法都有哪些比较大的挑战或者说限制然后我们这个研究是怎么样去聚焦于解决这些问题的传统的方法呢它主要是基于第一性原理的能量函数那它会面临几个比较大的问题一个是序列空间随着长度是指数级增长的所以这是一个非常大的搜索空间第二个是你在搜索的时候很容易陷入局部最优然后导致你没有办法找到全局最优的解那近年来呢深度学习的方法其实展示了非常大的潜力比如说像Diffusion Model可以去产生非常精确的Backbone像Protein MPNN可以去针对一个Backbone去设计出Sequence但是呢这些方法在治疗性的设计这个领域里面其实还是比较少的那主要用的还是一些已经比较成熟的分子类型比如抗体听起来就是说这个深度学习的方法虽然很有潜力但是好像在实际应用上面还是有一些瓶颈需要去突破的对 是的尤其是在多突变位点的重新设计这个问题上面现在的工具它其实主要的验证还是在单点和双点的突变的数据集上面那它很难去真正的反映治疗性设计这个场景下面的真实的需求因为你可能需要去做一些同时很多个位点的突变然后你要去预测它的结构和功能的变化这个其实是非常难的所以我们这个研究呢就是针对这样的一个挑战我们选取了两类比较有代表性的方法去在多个场景下面去评估它们的能力那我们第一个挑战就是说要评估不同的蛋白质设计的工具和方法在面对多个位点同时突变的时候的表现那我们是怎么做的呢我们首先呢是建立了一个数据集这个数据集呢是包含了36个Spectrum SH3结构域的输水和蛋白质设计的方法核心的多突变体然后这些突变体呢我们都有非常精确的化学变性的稳定性的数据其中有16个是我们用我们的新方法Track and Bind设计的我们解析了其中7个突变体的晶体结构所以我们现在对于这个SH3结构域的这个Heroformic Core的突变的情况是有一个比较深入的理解的听起来你们这个数据集真的是下了大功夫是啊然后我们还补充了从文献当中选取的另外15个来自于不同蛋白质的多突变体的数据我们总共测试了三类方法第一类方法呢是基于序列来预测结构的比如说像ESMFold L Fold 2Roast Fold第二类方法呢是从Backbone来预测序列的比如说像ESM InverseProtein NPNN和我们的Track and Bind第三类呢是基于Force Field的方法比如说像Rosetta和Fold X我们通过这样的一个多维度的测试呢就可以比较全面的去看到每一种方法它的优点和缺点到底在哪里为我们在实际应用当中去选择合适的方法我们第二个挑战是要评估这个Inverse Folding的工具在预测有很多突变的稳定的变体上面的表现那我们是怎么做的呢我们这次呢是使用了一个非常大的数据集这个数据集呢是包含了超过16万个Single和Double Mutants的Free Energy的变化然后它是来自于155个Artificial Domains和58个Natural Domains我们把这个实验测到的Fitness的数据呢和这些工具预测的序列进行了一个比较这些工具呢包括ESM InverseProtein NPNNFold XRosetta和我们的Track and Bind对我们的Track and Bind呢在这个里面不光是用来设计Mutant我们还把它当做一个scoring function来使用听起来是一个fitting function呢?是一个非常全面的评估啊对是的我们这个里面涵盖了几乎所有的比较主流的工具然后通过这样的一个评估我们就可以看到比如说像Track and Bind和Protein NPNN这样的方法它在预测这些稳定的变体上面是表现非常好的同时我们也发现就是没有一种方法是完美的所以可能未来还是需要把多种方法结合起来才能够得到一个更好的结果我们使用Track and Bind来设计SH3结构域的多突变体的变体然后我们是怎么做的呢?结果怎么样呢?我们选择了这个SH3结构域的hydrophobic core里面的9个residues然后我们使用了两个模板一个是它的野生型的结构ESHG和一个它的variant EE6G我们分别设计了8个Mutants总共是16个Mutants那我们在设计的过程当中呢我们是首先把这9个核心的residues都突变成了Alanine我们在运行Track and Bind的时候呢我们是排除了Charged Residues和一些Large Polar Residues我们只选择那些至少有两个Internal Sidechain Contacts的这样的tricks我们最后得到了Top500的这样的combinations我们又经过了FoldX的refinement最后选择了16个Canadase来进行实验验证听起来是一个非常系统的设计和筛选的过程啊对 是的然后我们实验表达纯化了这16个Mutants并且测了它们的Folding Stability我们发现呢有5个Mutants它的稳定性是等于或者高于野生型的有6个Mutants是虽然稳定性下降了但是它还是Folded的有两个Mutants是Partially Unfolded还有三个Mutants是Mainly Unfolded那我们同时呢也用FoldX去model了这些设计我们发现就是如果我们用野生型的结构作为模板的话它的预测和实验的数据是比较相符的但是如果我们用这个Expanded 16G作为模板的话它的预测就会差得比较远这个也是跟FoldX它本身没有办法很好的处理有张力的结构是有关系的我们在对这些有很多突变的SH3的变体进行研究的时候我们是怎么去分析这个实验测到的稳定性和这些工具预测的结果之间的关系的呢我们首先呢是把我们自己测得的16个SH3的Mutants的稳定性的数据和文献当中的另外20个数据点合并到了一起所以我们总共有36个实验的DeltaG的数据然后我们用20种不同的Metrics对这些Mutants进行了预测那这些Metrics呢我们分成了四大类第一类是基于Fixed Backbone的Scoring Function比如说像Cheat Combine, ESM Inverse, Protein MPNN等等第二类呢是基于AI的方法直接从序列到结构预测再给你一个可靠性的分数比如说像ESM Fold, Roosted Fold, L Fold 2第三类呢是把我们的Mutants进行了预测把AI预测的结构再用FoldX或者Rosetta这样的Force Field来进行评估第四类呢是在第三类的基础上面再加上一个Relaxation的步骤哇听起来你们这个评估真的是非常全面啊对是的然后我们为了去比较这些方法呢我们计算了True Positive Rate, True Negative Rate, Balanced Accuracy我们也计算了实验的DeltaG和预测的Scores之间的Correlation我们把所有的这些结果呢都划成了一个比较直观的图从这个图上面我们可以看到比如说像ESM Fold F和RF1 Fold R这样的方法它的Balanced Accuracy是可以达到08以上的那这个就是一个比较好的方法那我们今天聊了这么多啊从传统的蛋白质设计的挑战到深度学习方法的潜力然后再到我们这个Track&Bind以及其他的工具的全面的评估可以看到就是说现在这个领域还是在快速的发展的未来可能还是会需要把多种方法结合起来才能够去解决这些比较复杂的问题对 OK了