1
00:00:00,000 --> 00:00:01,600
Have you ever stopped to think about

2
00:00:01,600 --> 00:00:06,400
how these really tiny, totally invisible changes

3
00:00:06,400 --> 00:00:08,520
happen at the molecular level?

4
00:00:08,520 --> 00:00:11,120
Inside proteins specifically,

5
00:00:11,120 --> 00:00:14,680
and how those tiny shifts can have just massive impacts

6
00:00:14,680 --> 00:00:18,520
on health, medicine, even industry, it's kind of wild.

7
00:00:18,520 --> 00:00:19,360
It really is.

8
00:00:19,360 --> 00:00:21,560
Protein stability, that's the bedrock, right?

9
00:00:21,560 --> 00:00:24,040
How well it holds its shape is crucial for what it does.

10
00:00:24,040 --> 00:00:26,000
Exactly, and here's the kicker.

11
00:00:26,000 --> 00:00:28,840
Even changing one single amino acid,

12
00:00:28,840 --> 00:00:30,560
just one tiny building block

13
00:00:30,560 --> 00:00:32,520
can throw that stability way off.

14
00:00:32,520 --> 00:00:35,240
Yeah, completely change its function, or lack thereof.

15
00:00:35,240 --> 00:00:37,560
So the big crooked the puzzle scientists have wrestled with

16
00:00:37,560 --> 00:00:39,800
is how do we actually predict those changes,

17
00:00:39,800 --> 00:00:42,160
and importantly, their consequences.

18
00:00:42,160 --> 00:00:43,400
It's been a tough nut to crack.

19
00:00:43,400 --> 00:00:45,760
Well, that's exactly what we're getting into today.

20
00:00:45,760 --> 00:00:47,080
This deep dive is centered

21
00:00:47,080 --> 00:00:49,760
on a really fascinating new research paper.

22
00:00:49,760 --> 00:00:52,640
It introduces a computational method called DP-STAB.

23
00:00:52,640 --> 00:00:53,680
DP-STAB, right.

24
00:00:53,680 --> 00:00:55,640
And we're gonna explore how this new approach

25
00:00:55,640 --> 00:00:57,640
might finally be cracking.

26
00:00:57,920 --> 00:01:00,080
One of elective biology's really critical,

27
00:01:00,080 --> 00:01:01,560
complex challenges.

28
00:01:01,560 --> 00:01:03,200
Yeah, and our mission here really

29
00:01:03,200 --> 00:01:05,560
is to unpack DP-STAB for you.

30
00:01:05,560 --> 00:01:07,560
Whether you're prepping for a meeting

31
00:01:07,560 --> 00:01:10,080
or just curious about the latest science.

32
00:01:10,080 --> 00:01:13,000
Or maybe just fascinated by how AI is changing things.

33
00:01:13,000 --> 00:01:14,000
Exactly.

34
00:01:14,000 --> 00:01:16,600
We wanna show you how DP-STAB predicts the effects

35
00:01:16,600 --> 00:01:18,720
of these single mutations on stability,

36
00:01:18,720 --> 00:01:21,000
and crucially, why that actually matters.

37
00:01:21,000 --> 00:01:23,320
Because it's not just academic curiosity, is it?

38
00:01:23,320 --> 00:01:24,160
Not at all.

39
00:01:24,200 --> 00:01:27,000
This is a breakthrough with real potential

40
00:01:27,000 --> 00:01:29,400
for medicine, biotech,

41
00:01:29,400 --> 00:01:31,480
maybe even greener industries down the line.

42
00:01:31,480 --> 00:01:34,400
Okay, so we're talking tiny molecular changes.

43
00:01:34,400 --> 00:01:35,840
Huge impacts.

44
00:01:35,840 --> 00:01:38,360
Before we jump into the solution, DP-STAB,

45
00:01:38,360 --> 00:01:41,040
let's really focus on the problem itself.

46
00:01:41,040 --> 00:01:43,360
Why has this been so difficult,

47
00:01:43,360 --> 00:01:45,000
predicting these stability changes?

48
00:01:45,000 --> 00:01:47,160
Yeah, the ophegie and dectorium values.

49
00:01:47,160 --> 00:01:50,120
Right, titrine is the change in Gibbs free energy,

50
00:01:50,120 --> 00:01:51,840
sort of a measure of stability change.

51
00:01:51,880 --> 00:01:55,040
And DP team is the melting temperature change.

52
00:01:55,040 --> 00:01:55,880
Knowing these.

53
00:01:55,880 --> 00:01:57,680
It's gold dust for protein engineering, right?

54
00:01:57,680 --> 00:01:58,640
Or designing new drugs.

55
00:01:58,640 --> 00:01:59,680
Absolutely invaluable.

56
00:01:59,680 --> 00:02:01,280
If you wanna make a protein more stable

57
00:02:01,280 --> 00:02:03,480
or design a drug that interacts correctly,

58
00:02:03,480 --> 00:02:04,600
you need to predict this stuff.

59
00:02:04,600 --> 00:02:06,960
But yeah, accuracy has been a massive hurdle.

60
00:02:06,960 --> 00:02:08,960
So what were the old ways of trying?

61
00:02:08,960 --> 00:02:12,040
Well, historically, there were two main paths.

62
00:02:12,040 --> 00:02:14,840
First, you had structure-based methods.

63
00:02:14,840 --> 00:02:15,680
Okay, it makes sense.

64
00:02:15,680 --> 00:02:16,760
You look at the 3D structure.

65
00:02:16,760 --> 00:02:17,600
Exactly.

66
00:02:17,600 --> 00:02:21,720
You need a high quality, detailed 3D map of the protein,

67
00:02:21,720 --> 00:02:25,200
like needing the exact blueprint of an engine

68
00:02:25,200 --> 00:02:28,240
to see how changing one tiny part affects it.

69
00:02:28,240 --> 00:02:29,080
Makes sense.

70
00:02:29,080 --> 00:02:29,920
What's the catch?

71
00:02:29,920 --> 00:02:31,840
The catch is getting that blueprint.

72
00:02:31,840 --> 00:02:34,320
High quality structures are often really hard,

73
00:02:34,320 --> 00:02:36,440
sometimes impossible to get.

74
00:02:36,440 --> 00:02:38,840
So these methods just weren't applicable

75
00:02:38,840 --> 00:02:40,160
in many, many cases.

76
00:02:40,160 --> 00:02:42,080
No structure, no prediction.

77
00:02:42,080 --> 00:02:43,080
Okay, so that's limited.

78
00:02:43,080 --> 00:02:44,040
What was the alternative?

79
00:02:44,040 --> 00:02:47,000
The other main approach was sequence-based methods.

80
00:02:47,000 --> 00:02:49,560
These are way more appealing for large-scale stuff

81
00:02:49,560 --> 00:02:52,680
because you only need the protein's amino acid sequence.

82
00:02:52,680 --> 00:02:55,240
Which is just the string of letters, the basic code.

83
00:02:55,240 --> 00:02:57,720
Right, and we have tons of sequence data.

84
00:02:57,720 --> 00:02:58,920
Much easier to get.

85
00:02:58,920 --> 00:02:59,760
But they struggled.

86
00:02:59,760 --> 00:03:00,600
Why was that?

87
00:03:00,600 --> 00:03:01,560
They struggled, yeah.

88
00:03:01,560 --> 00:03:04,480
Because fundamentally, they couldn't really see

89
00:03:04,480 --> 00:03:06,880
the structural changes happening locally

90
00:03:06,880 --> 00:03:08,200
right around the mutation site.

91
00:03:08,200 --> 00:03:09,040
Ah, okay.

92
00:03:09,040 --> 00:03:10,760
So even if the whole protein

93
00:03:10,760 --> 00:03:12,520
didn't change shape drastically.

94
00:03:12,520 --> 00:03:13,560
Exactly.

95
00:03:13,560 --> 00:03:17,200
Those subtle shifts in how atoms interact nearby,

96
00:03:17,200 --> 00:03:19,400
those are critical for stability.

97
00:03:19,400 --> 00:03:21,120
And sequence methods, well,

98
00:03:21,120 --> 00:03:23,480
they just couldn't capture that nuance effectively.

99
00:03:23,480 --> 00:03:25,960
They were missing that local structural context.

100
00:03:25,960 --> 00:03:27,360
And there was another problem too, wasn't there?

101
00:03:27,360 --> 00:03:29,080
Something about the data itself.

102
00:03:29,080 --> 00:03:31,640
Oh yeah, a big one.

103
00:03:31,640 --> 00:03:32,640
Data imbalance.

104
00:03:32,640 --> 00:03:33,480
Meaning.

105
00:03:33,480 --> 00:03:36,400
Meaning that when you look at the experimental data we have,

106
00:03:36,400 --> 00:03:39,600
mutations that destabilize a protein

107
00:03:39,600 --> 00:03:41,680
are far, far more common

108
00:03:41,680 --> 00:03:43,680
than mutations that make it more stable.

109
00:03:43,680 --> 00:03:45,720
Which is often what you want for engineering, right?

110
00:03:45,720 --> 00:03:46,800
A more stable protein.

111
00:03:46,800 --> 00:03:47,640
Precisely.

112
00:03:47,640 --> 00:03:50,360
So the data sets used to train prediction models

113
00:03:50,360 --> 00:03:53,200
are heavily skewed towards destabilizing examples.

114
00:03:53,200 --> 00:03:54,560
Which can bias the models.

115
00:03:54,560 --> 00:03:55,400
Yeah.

116
00:03:55,400 --> 00:03:57,240
Make them less likely to find those rare,

117
00:03:57,240 --> 00:03:58,840
valuable, stabilizing ones.

118
00:03:58,840 --> 00:03:59,800
Exactly.

119
00:03:59,800 --> 00:04:02,160
The models might just learn to predict destabilizing

120
00:04:02,160 --> 00:04:03,000
most of the time,

121
00:04:03,000 --> 00:04:05,160
because that's what they see most often in the training data.

122
00:04:05,160 --> 00:04:07,040
Okay, so we've got missing structures,

123
00:04:07,040 --> 00:04:09,400
methods that can't see local changes,

124
00:04:09,400 --> 00:04:10,640
and biased data.

125
00:04:10,640 --> 00:04:12,120
That's quite a mountain to climb.

126
00:04:12,120 --> 00:04:15,640
It really sets the stage for why a new approach was needed.

127
00:04:15,640 --> 00:04:17,440
All right, so enter DP step.

128
00:04:17,440 --> 00:04:19,000
This is where it gets really interesting.

129
00:04:19,000 --> 00:04:23,760
The paper proposes this as a novel,

130
00:04:23,760 --> 00:04:26,280
sequence-based deep learning solution.

131
00:04:26,280 --> 00:04:28,640
Designed specifically to tackle all those limitations

132
00:04:28,640 --> 00:04:29,480
we just laid out.

133
00:04:29,480 --> 00:04:30,320
That's the goal.

134
00:04:30,320 --> 00:04:31,960
To get the kind of insight you'd expect

135
00:04:31,960 --> 00:04:33,600
from structure-based methods,

136
00:04:33,600 --> 00:04:35,240
but using only the sequence,

137
00:04:35,240 --> 00:04:36,800
making it much more scalable.

138
00:04:36,800 --> 00:04:37,800
So how does it do that?

139
00:04:37,800 --> 00:04:39,120
What's the core idea?

140
00:04:39,120 --> 00:04:42,040
The core innovation is using a very powerful,

141
00:04:42,040 --> 00:04:45,480
protein-large language model, a PLLM.

142
00:04:45,480 --> 00:04:48,600
Like the AI models that write text, but for protein.

143
00:04:48,600 --> 00:04:49,560
Exactly like that.

144
00:04:49,560 --> 00:04:51,680
It's trained on truly massive data sets

145
00:04:51,680 --> 00:04:52,800
of protein sequences.

146
00:04:52,800 --> 00:04:54,720
It learns the language of proteins,

147
00:04:54,720 --> 00:04:57,120
how they evolve, how residues tend to interact.

148
00:04:57,120 --> 00:05:00,440
Specifically, DP step uses a model called ESM-2.

149
00:05:00,440 --> 00:05:02,280
ESM-2, okay, I've heard of that one.

150
00:05:02,280 --> 00:05:03,720
Yeah, it's state-of-the-art.

151
00:05:03,720 --> 00:05:06,720
So ESM-2 helps DP step understand evolutionary patterns,

152
00:05:06,720 --> 00:05:08,400
but here's the clever bit.

153
00:05:08,400 --> 00:05:10,680
It can also infer conformational information

154
00:05:10,680 --> 00:05:11,520
from the sequence.

155
00:05:11,520 --> 00:05:13,920
Confirmational, like shape information.

156
00:05:13,920 --> 00:05:17,160
Sort of, it can predict things like residue contact maps.

157
00:05:17,160 --> 00:05:18,960
Basically, which amino acids are likely

158
00:05:18,960 --> 00:05:20,560
to be physically close to each other

159
00:05:20,560 --> 00:05:22,440
in the folded 3D structure?

160
00:05:22,440 --> 00:05:25,120
Ah, so it's predicting structural features

161
00:05:25,120 --> 00:05:27,720
without needing the actual experimental structure.

162
00:05:27,720 --> 00:05:31,120
Exactly, it's like inferring the shape of a complex,

163
00:05:31,120 --> 00:05:33,600
not just by looking at the sequence of the string.

164
00:05:33,600 --> 00:05:35,760
It gives it that missing structural context.

165
00:05:35,760 --> 00:05:38,240
Wow, okay, that's clever.

166
00:05:38,240 --> 00:05:40,120
But you mentioned some secret sauce too,

167
00:05:40,120 --> 00:05:41,360
specific strategies.

168
00:05:41,360 --> 00:05:42,900
Right, there are a couple of key things

169
00:05:42,900 --> 00:05:44,860
that make DP-STUG really stand out.

170
00:05:44,860 --> 00:05:49,060
First is this self-distillation inference strategy.

171
00:05:49,060 --> 00:05:51,740
Self-distillation, what does that mean in practice?

172
00:05:51,740 --> 00:05:54,300
It sounds complicated, but it's a smart way

173
00:05:54,300 --> 00:05:56,340
for the model to teach itself and improve.

174
00:05:56,340 --> 00:06:00,940
Imagine the model makes predictions on some data,

175
00:06:00,940 --> 00:06:02,860
then it uses those own predictions

176
00:06:02,860 --> 00:06:04,580
as additional training examples,

177
00:06:04,580 --> 00:06:06,100
but only the confident ones.

178
00:06:06,100 --> 00:06:08,580
So it refines its knowledge using its own output?

179
00:06:08,580 --> 00:06:10,340
Pretty much, it enriches the training data,

180
00:06:10,340 --> 00:06:12,620
helps the model learn more robust patterns,

181
00:06:12,620 --> 00:06:14,380
and makes it better at generalizing,

182
00:06:14,380 --> 00:06:16,060
applying its knowledge to new proteins

183
00:06:16,060 --> 00:06:17,140
that hasn't seen before.

184
00:06:17,140 --> 00:06:18,700
Okay, that's one part, what was the other?

185
00:06:18,700 --> 00:06:21,180
The other key strategy is the anti-symmetric constraint.

186
00:06:21,180 --> 00:06:22,900
Anti-symmetric, okay, break that down.

187
00:06:22,900 --> 00:06:25,460
It boils down to basic logic.

188
00:06:25,460 --> 00:06:28,380
If you mutate an amino acid, say from A to B,

189
00:06:28,380 --> 00:06:31,380
and that changes the stability by a certain amount,

190
00:06:31,380 --> 00:06:33,380
let's say it decreases it by two units.

191
00:06:33,380 --> 00:06:34,220
Okay.

192
00:06:34,220 --> 00:06:36,500
Then logically, mutating it back from B to A

193
00:06:36,500 --> 00:06:38,020
should do the exact opposite, right?

194
00:06:38,020 --> 00:06:39,860
It should increase stability by two units.

195
00:06:39,860 --> 00:06:41,140
Makes perfect sense.

196
00:06:41,180 --> 00:06:42,900
DGPG should just flip its sign.

197
00:06:42,900 --> 00:06:43,740
Exactly.

198
00:06:43,740 --> 00:06:47,100
DGB should equal negative DBA.

199
00:06:47,100 --> 00:06:49,380
But older models didn't always follow this rule.

200
00:06:49,380 --> 00:06:50,500
Really?

201
00:06:50,500 --> 00:06:52,180
That seems like a fundamental property.

202
00:06:52,180 --> 00:06:55,380
It is, but their architecture didn't enforce it.

203
00:06:55,380 --> 00:06:58,460
DPSTAP explicitly builds in this constraint.

204
00:06:58,460 --> 00:07:00,740
It forces the model to learn and respect

205
00:07:00,740 --> 00:07:04,380
this fundamental symmetry, or rather, anti-symmetry.

206
00:07:04,380 --> 00:07:07,220
Which makes the predictions more physically realistic

207
00:07:07,220 --> 00:07:08,060
and reliable.

208
00:07:08,060 --> 00:07:08,900
Much more robust, yes.

209
00:07:08,900 --> 00:07:10,780
It ensures that internal consistency.

210
00:07:10,780 --> 00:07:13,020
Okay, so we have the PLMM for context,

211
00:07:13,020 --> 00:07:14,980
self-distillation for better learning,

212
00:07:14,980 --> 00:07:16,620
and this anti-symmetric constraint

213
00:07:16,620 --> 00:07:18,260
for logical consistency.

214
00:07:18,260 --> 00:07:19,420
Can we peek into the hood a bit?

215
00:07:19,420 --> 00:07:20,940
How are these pieces put together?

216
00:07:20,940 --> 00:07:21,780
Sure.

217
00:07:21,780 --> 00:07:24,740
DPSTAP basically has three main modules working together.

218
00:07:24,740 --> 00:07:26,620
First, the sequential encoder.

219
00:07:26,620 --> 00:07:27,900
That's the ESM2 part.

220
00:07:27,900 --> 00:07:29,940
Yep, powered by ESM2.

221
00:07:29,940 --> 00:07:32,500
Its job is to take the protein sequence

222
00:07:32,500 --> 00:07:35,820
and extract all that rich evolutionary information,

223
00:07:35,820 --> 00:07:37,820
and also predict those contact maps.

224
00:07:37,820 --> 00:07:40,100
We talked about the inferred structural information.

225
00:07:40,100 --> 00:07:42,140
It's the initial processing unit.

226
00:07:42,140 --> 00:07:43,180
Got it. What's next?

227
00:07:43,180 --> 00:07:44,940
Next comes the neighboring encoder.

228
00:07:44,940 --> 00:07:47,620
This is where it really zooms in on the mutation site.

229
00:07:47,620 --> 00:07:49,220
Focusing on the local environment.

230
00:07:49,220 --> 00:07:50,260
Exactly.

231
00:07:50,260 --> 00:07:53,580
It uses something called a cross-attention mechanism.

232
00:07:53,580 --> 00:07:55,820
Think of it like giving the model the ability

233
00:07:55,820 --> 00:07:58,260
to pay special attention to the mutated residue

234
00:07:58,260 --> 00:07:59,780
and its important neighbors.

235
00:07:59,780 --> 00:08:02,940
It figures out which nearby residues are most relevant.

236
00:08:02,940 --> 00:08:04,580
And it integrates information from them.

237
00:08:04,580 --> 00:08:05,860
Yes, and critically,

238
00:08:05,860 --> 00:08:08,780
it can also capture long-range interactions.

239
00:08:08,780 --> 00:08:10,900
Sometimes residues far apart in the sequence

240
00:08:10,900 --> 00:08:13,980
actually fold together and interact in 3D space.

241
00:08:13,980 --> 00:08:16,220
This module helps capture those important,

242
00:08:16,220 --> 00:08:19,380
non-local effects that simpler methods often miss.

243
00:08:19,380 --> 00:08:20,900
Okay, so sequence info,

244
00:08:20,900 --> 00:08:23,620
then focused local and long-range info.

245
00:08:23,620 --> 00:08:24,620
What's the final step?

246
00:08:24,620 --> 00:08:27,180
The final piece is the feature transition module.

247
00:08:27,180 --> 00:08:29,380
This module takes all the information gathered

248
00:08:29,380 --> 00:08:31,820
about the original protein, the wild type,

249
00:08:31,820 --> 00:08:33,060
and the mutated version.

250
00:08:33,060 --> 00:08:33,900
Just binds them.

251
00:08:33,900 --> 00:08:35,860
Right, it integrates features from both,

252
00:08:35,860 --> 00:08:37,380
and importantly, it also factors in

253
00:08:37,380 --> 00:08:39,860
environmental conditions like pH and temperature,

254
00:08:39,860 --> 00:08:41,620
especially for the age predictions,

255
00:08:41,620 --> 00:08:44,020
because stability can depend on those factors too.

256
00:08:44,020 --> 00:08:46,260
And then it spits out the final prediction,

257
00:08:46,260 --> 00:08:48,580
the AG or aid value.

258
00:08:48,580 --> 00:08:50,980
Precisely, it synthesizes everything

259
00:08:50,980 --> 00:08:53,700
to make that final call on the stability change.

260
00:08:53,700 --> 00:08:55,820
Okay, that sounds incredibly sophisticated.

261
00:08:55,820 --> 00:08:57,540
So the million dollar question,

262
00:08:58,380 --> 00:09:00,460
how well does it actually work?

263
00:09:00,460 --> 00:09:02,100
Did all this complexity pay off?

264
00:09:02,100 --> 00:09:02,940
Oh, absolutely.

265
00:09:02,940 --> 00:09:05,900
The results reported are really quite striking.

266
00:09:05,900 --> 00:09:08,940
DP-STAB seems to be setting a new state of the art.

267
00:09:08,940 --> 00:09:09,780
Across the board.

268
00:09:09,780 --> 00:09:10,700
Pretty much, yeah.

269
00:09:10,700 --> 00:09:12,180
Let's talk AG prediction first,

270
00:09:12,180 --> 00:09:13,580
that energy change measure.

271
00:09:13,580 --> 00:09:16,500
DP-STAB significantly lowers the prediction errors

272
00:09:16,500 --> 00:09:18,100
compared to previous methods.

273
00:09:18,100 --> 00:09:20,420
Lower errors meaning more accurate.

274
00:09:20,420 --> 00:09:21,340
Much more accurate.

275
00:09:21,340 --> 00:09:24,620
The RMSC, which is a kind of average error,

276
00:09:24,620 --> 00:09:27,740
dropped below 0.93 kilocal.

277
00:09:27,740 --> 00:09:31,860
The MAE, another error measure, is below 0.68 kilomole.

278
00:09:31,860 --> 00:09:33,180
Lower is better here.

279
00:09:33,180 --> 00:09:34,660
And how well did the predictions match

280
00:09:34,660 --> 00:09:36,380
the actual lab experiments?

281
00:09:36,380 --> 00:09:37,220
Very well.

282
00:09:37,220 --> 00:09:38,700
The correlation coefficients,

283
00:09:38,700 --> 00:09:41,700
which measure how well predictions line up with reality,

284
00:09:41,700 --> 00:09:42,820
are really high.

285
00:09:42,820 --> 00:09:45,780
SEC, the Spearman correlation hit 0.86,

286
00:09:45,780 --> 00:09:49,900
and PCC, the Pearson correlation reached 0.84.

287
00:09:49,900 --> 00:09:52,260
Closer to one is better, so those are excellent scores.

288
00:09:52,260 --> 00:09:55,020
Wow, okay, that's impressive on the continuous values.

289
00:09:55,020 --> 00:09:57,100
What about just classifying mutations?

290
00:09:57,100 --> 00:10:01,020
Like is it stabilizing, destabilizing, or neutral?

291
00:10:01,020 --> 00:10:02,620
That's often the practical question

292
00:10:02,620 --> 00:10:03,780
you want answered quickly.

293
00:10:03,780 --> 00:10:06,700
And here too, DP-STAB performs exceptionally well.

294
00:10:06,700 --> 00:10:09,900
Its classification accuracy is over 74%.

295
00:10:09,900 --> 00:10:11,620
So roughly three out of four times,

296
00:10:11,620 --> 00:10:14,500
it correctly flags whether a mutation will strengthen,

297
00:10:14,500 --> 00:10:16,620
weaken, or not affect the protein.

298
00:10:16,620 --> 00:10:17,460
That's right.

299
00:10:17,460 --> 00:10:21,900
In their tests, it correctly classified 684 out of 922 cases.

300
00:10:21,900 --> 00:10:24,420
That's a huge improvement, and really useful

301
00:10:24,420 --> 00:10:27,300
for screening potential mutations in drug design

302
00:10:27,300 --> 00:10:28,700
or enzyme engineering.

303
00:10:28,700 --> 00:10:31,100
Cuts down the lab work significantly, I imagine.

304
00:10:31,100 --> 00:10:33,340
Dramatically, saves time and resources.

305
00:10:33,340 --> 00:10:35,060
And you mentioned team prediction too,

306
00:10:35,060 --> 00:10:36,180
the melting temperature.

307
00:10:36,180 --> 00:10:39,820
Yes, performance there was also reported as strong

308
00:10:39,820 --> 00:10:42,340
or at least comparable to the best existing methods.

309
00:10:42,340 --> 00:10:44,180
So it's delivering on multiple fronts.

310
00:10:44,180 --> 00:10:46,340
Can we see it in action, like specific examples?

311
00:10:46,340 --> 00:10:49,140
Yeah, the paper highlights how well DP-STAB performs

312
00:10:49,140 --> 00:10:51,540
on individual proteins, which is crucial

313
00:10:51,540 --> 00:10:53,580
for practical engineering tasks.

314
00:10:53,580 --> 00:10:56,340
Compared to older methods, they saw median improvements

315
00:10:56,340 --> 00:11:00,260
of like 9% in SEC, 7.5% in PCC,

316
00:11:00,300 --> 00:11:03,420
and almost 20% in classification accuracy per protein.

317
00:11:03,420 --> 00:11:04,900
So it's not just good on average.

318
00:11:04,900 --> 00:11:07,700
It's reliably better for specific cases you might care about.

319
00:11:07,700 --> 00:11:08,620
Exactly.

320
00:11:08,620 --> 00:11:11,540
They showed a case study, Protein 2-PTLA,

321
00:11:11,540 --> 00:11:13,980
with an A34G mutation.

322
00:11:13,980 --> 00:11:16,300
DP-STAB predicted a stability decrease,

323
00:11:16,300 --> 00:11:18,860
a DG of Monash 1.2 kilomole.

324
00:11:18,860 --> 00:11:19,700
And the real value?

325
00:11:19,700 --> 00:11:22,260
The experimental value was 9.2.1 kilomole,

326
00:11:22,260 --> 00:11:23,380
so quite close.

327
00:11:23,380 --> 00:11:25,380
But importantly, a previous leading method

328
00:11:25,380 --> 00:11:26,780
predicted it as neutral,

329
00:11:26,780 --> 00:11:28,780
completely missing the destabilization.

330
00:11:28,780 --> 00:11:30,460
Why did DP-STAB get it right?

331
00:11:30,460 --> 00:11:31,980
Did it offer an explanation?

332
00:11:31,980 --> 00:11:33,340
Yes, that's the beauty.

333
00:11:33,340 --> 00:11:35,580
The analysis suggested that replacing alanine A

334
00:11:35,580 --> 00:11:38,500
with a smaller glycine G led to a loss of key interactions

335
00:11:38,500 --> 00:11:41,540
with nearby residues, like Y50 and F36.

336
00:11:41,540 --> 00:11:43,380
It pinpointed the structural reason.

337
00:11:43,380 --> 00:11:45,260
Ah, providing insight, not just a number.

338
00:11:45,260 --> 00:11:46,100
Precisely.

339
00:11:46,100 --> 00:11:49,500
Another example, Protein 106XA, mutation H51A.

340
00:11:49,500 --> 00:11:52,220
DP-STAB predicted managed 0.8 kilomole.

341
00:11:52,220 --> 00:11:54,180
The actual was managed 0.7 kilomole.

342
00:11:54,180 --> 00:11:57,540
Again, very close, and the old methods.

343
00:11:57,540 --> 00:12:01,020
A prior method incorrectly called it stable.

344
00:12:01,020 --> 00:12:03,340
DP-STAB's analysis pointed to the loss

345
00:12:03,340 --> 00:12:04,900
of important hydrogen bonds

346
00:12:04,900 --> 00:12:07,220
caused by removing the histidine H.

347
00:12:07,220 --> 00:12:10,700
It's seeing those crucial atomic level interactions.

348
00:12:10,700 --> 00:12:13,180
It seems to be capturing them much better, yes.

349
00:12:13,180 --> 00:12:14,940
These specific accurate insights

350
00:12:14,940 --> 00:12:17,100
are game changers for protein engineers.

351
00:12:17,100 --> 00:12:18,500
What about scalability?

352
00:12:18,500 --> 00:12:20,460
Can it handle really big data sets?

353
00:12:20,460 --> 00:12:22,940
You mentioned debutational scanning, DMS.

354
00:12:22,940 --> 00:12:26,100
Yeah, DMS experiments generate data on thousands,

355
00:12:26,100 --> 00:12:29,620
even tens of thousands, of mutations for a single protein.

356
00:12:29,620 --> 00:12:31,780
It's a massive amount of information.

357
00:12:31,780 --> 00:12:32,780
A DP-STAB.

358
00:12:32,780 --> 00:12:34,900
It performed very well there, too.

359
00:12:34,900 --> 00:12:37,500
On data sets with around 20,000 mutations,

360
00:12:37,500 --> 00:12:39,620
DP-STAB showed significant improvements

361
00:12:39,620 --> 00:12:40,980
in correlation metrics.

362
00:12:40,980 --> 00:12:45,660
SCC boosted by 13%, Kendall correlation by 15%.

363
00:12:45,660 --> 00:12:46,500
It appears so.

364
00:12:46,500 --> 00:12:49,060
It's efficient enough for these large-scale analyses,

365
00:12:49,060 --> 00:12:50,820
which opens up a lot of possibilities

366
00:12:50,820 --> 00:12:53,500
for exploring protein function landscapes more deeply.

367
00:12:53,500 --> 00:12:54,340
It's huge.

368
00:12:54,380 --> 00:12:57,540
And there's another cool insight from the DMS data.

369
00:12:57,540 --> 00:13:02,060
When DP-STAB ranks mutations by predicted stability change,

370
00:13:02,060 --> 00:13:04,140
the top-ranked ones, the ones it predicts,

371
00:13:04,140 --> 00:13:07,700
have the biggest effect, generally correspond to mutations

372
00:13:07,700 --> 00:13:11,100
that truly have higher experimental DJI values.

373
00:13:11,100 --> 00:13:12,140
Meaning?

374
00:13:12,140 --> 00:13:13,700
Meaning its strongest predictions

375
00:13:13,700 --> 00:13:15,460
are more likely to be identifying

376
00:13:15,460 --> 00:13:18,460
those genuinely significant stabilizing mutations,

377
00:13:18,460 --> 00:13:21,020
the ones researchers are often hunting for.

378
00:13:21,020 --> 00:13:23,780
It acts like a better filter for finding the good stuff.

379
00:13:23,780 --> 00:13:25,380
Finding the needles in the haystack.

380
00:13:25,380 --> 00:13:27,060
And much more effective filter, yes.

381
00:13:27,060 --> 00:13:29,580
Okay, so the performance is clearly top-notch.

382
00:13:29,580 --> 00:13:30,860
Let's circle back to why.

383
00:13:30,860 --> 00:13:33,180
You mentioned those two key strategies,

384
00:13:33,180 --> 00:13:36,540
self-distillation and the anti-symmetric constraint.

385
00:13:36,540 --> 00:13:37,940
How critical were they, really?

386
00:13:37,940 --> 00:13:40,900
They seem absolutely critical based on the ablation studies

387
00:13:40,900 --> 00:13:43,420
where they tested the model with those components removed.

388
00:13:43,420 --> 00:13:45,500
What happened when they took out self-distillation?

389
00:13:45,500 --> 00:13:48,060
Performance dropped noticeably across the board.

390
00:13:48,060 --> 00:13:50,980
For DG prediction, correlations like SCC and PCC

391
00:13:50,980 --> 00:13:52,780
went down by about 3%.

392
00:13:52,780 --> 00:13:55,220
And the errors, RMSE and MAE,

393
00:13:55,220 --> 00:13:57,940
went up by 6% and 4% respectively.

394
00:13:57,940 --> 00:13:59,260
So not a minor tweak then.

395
00:13:59,260 --> 00:14:01,020
It really helps the model learn better.

396
00:14:01,020 --> 00:14:01,860
Definitely.

397
00:14:01,860 --> 00:14:03,220
It improves generalization.

398
00:14:03,220 --> 00:14:05,100
They even showed it helped when testing

399
00:14:05,100 --> 00:14:07,020
on completely independent data sets

400
00:14:07,020 --> 00:14:08,940
generated by different techniques,

401
00:14:08,940 --> 00:14:10,620
boosting performance further there too.

402
00:14:10,620 --> 00:14:12,140
It makes the model more robust.

403
00:14:12,140 --> 00:14:14,460
Okay, and the anti-symmetric constraint,

404
00:14:14,460 --> 00:14:18,180
by minus again, A to B change is the opposite of BDA.

405
00:14:18,180 --> 00:14:20,820
Right, that fundamental logical consistency.

406
00:14:20,820 --> 00:14:23,620
How badly did older models violate this?

407
00:14:23,620 --> 00:14:25,100
Pretty badly sometimes.

408
00:14:25,100 --> 00:14:26,620
Their predictions for ABMBA

409
00:14:26,620 --> 00:14:28,580
could be quite different inconsistent,

410
00:14:28,580 --> 00:14:31,340
which is problematic if you're relying on those predictions.

411
00:14:31,340 --> 00:14:34,380
And adding the constraint, fix this in DP-STAB.

412
00:14:34,380 --> 00:14:35,700
Significantly.

413
00:14:35,700 --> 00:14:38,060
By forcing the model to obey this rule,

414
00:14:38,060 --> 00:14:39,940
DP-STAB makes the prediction errors

415
00:14:39,940 --> 00:14:43,300
for these forward and reverse mutations much, much smaller.

416
00:14:43,300 --> 00:14:45,860
The outputs become almost perfectly opposite

417
00:14:45,860 --> 00:14:46,940
as they should be.

418
00:14:46,940 --> 00:14:48,100
Which just makes the whole system

419
00:14:48,100 --> 00:14:50,500
more trustworthy and practical for real use.

420
00:14:50,500 --> 00:14:53,140
Exactly, you can rely on that inverse relationship

421
00:14:53,140 --> 00:14:54,860
holding true in the predictions.

422
00:14:54,860 --> 00:14:56,820
It adds a layer of robustness.

423
00:14:56,820 --> 00:14:58,220
So let's pull this all together.

424
00:14:58,220 --> 00:14:59,980
What's the big picture here?

425
00:14:59,980 --> 00:15:03,060
What are the main advantages DP-STAB brings to the table?

426
00:15:03,060 --> 00:15:04,860
Well, first, it's sequence-based.

427
00:15:04,860 --> 00:15:07,140
That's huge, no need for those difficult

428
00:15:07,140 --> 00:15:08,500
to get 3D structures.

429
00:15:08,500 --> 00:15:09,740
Making it widely applicable.

430
00:15:09,740 --> 00:15:10,540
Yes.

431
00:15:10,540 --> 00:15:13,540
Second, it manages to accurately predict changes

432
00:15:13,540 --> 00:15:17,180
in residue contacts, essentially seeing local structural

433
00:15:17,180 --> 00:15:19,580
changes just from the sequence thanks to the PLLM.

434
00:15:19,580 --> 00:15:22,420
Getting structural insights without structures.

435
00:15:22,420 --> 00:15:23,020
Right.

436
00:15:23,020 --> 00:15:26,180
And third, it can pinpoint key residues involved

437
00:15:26,180 --> 00:15:29,220
in stability, like those forming hydrogen bonds,

438
00:15:29,220 --> 00:15:31,060
providing actionable insights.

439
00:15:31,060 --> 00:15:32,820
So for folks in protein engineering

440
00:15:32,820 --> 00:15:34,180
or biomedical research.

441
00:15:34,180 --> 00:15:37,740
It offers a fast, practical, and highly accurate tool.

442
00:15:37,740 --> 00:15:40,620
It can accelerate the process of designing better proteins,

443
00:15:40,620 --> 00:15:42,500
whether for therapies, diagnostics,

444
00:15:42,500 --> 00:15:44,380
or industrial applications.

445
00:15:44,380 --> 00:15:46,420
Less guesswork, faster discovery.

446
00:15:46,420 --> 00:15:47,900
That sounds like a major step forward.

447
00:15:47,900 --> 00:15:48,400
Yeah.

448
00:15:48,400 --> 00:15:49,300
What's next, though?

449
00:15:49,300 --> 00:15:51,580
What are the remaining challenges or future directions

450
00:15:51,580 --> 00:15:52,300
in this field?

451
00:15:52,300 --> 00:15:54,060
Oh, there are definitely challenges ahead.

452
00:15:54,060 --> 00:15:56,460
One is incorporating even finer details,

453
00:15:56,460 --> 00:15:59,980
like the specific orientations of amino acid side chains,

454
00:15:59,980 --> 00:16:01,260
which can be important.

455
00:16:01,260 --> 00:16:02,620
Getting even more granular.

456
00:16:02,620 --> 00:16:04,620
Yeah, adding more geometric detail.

457
00:16:04,620 --> 00:16:06,100
And maybe the biggest challenge looming

458
00:16:06,100 --> 00:16:08,800
is predicting the effects of multiple mutations

459
00:16:08,800 --> 00:16:10,180
happening at the same time.

460
00:16:10,180 --> 00:16:12,980
Ah, not just single changes, but combinations.

461
00:16:12,980 --> 00:16:13,820
Exactly.

462
00:16:13,820 --> 00:16:16,340
That adds layers and layers of complexity.

463
00:16:16,340 --> 00:16:18,100
Interactions between mutations can

464
00:16:18,100 --> 00:16:20,060
be really tricky to predict.

465
00:16:20,060 --> 00:16:23,460
That likely requires even more powerful AI approaches.

466
00:16:23,460 --> 00:16:24,940
Still mountains to climb, then.

467
00:16:24,940 --> 00:16:26,740
Always in science.

468
00:16:26,740 --> 00:16:29,060
So wrapping up, the core takeaway

469
00:16:29,060 --> 00:16:32,580
seems to be that DPSTAP is a really significant advance

470
00:16:32,580 --> 00:16:35,620
in how we understand and engineer proteins.

471
00:16:35,620 --> 00:16:39,060
It's a fantastic example of how sophisticated AI,

472
00:16:39,060 --> 00:16:40,700
like these large language models,

473
00:16:40,700 --> 00:16:43,580
can decode the complex rules of biology.

474
00:16:43,580 --> 00:16:44,380
Absolutely.

475
00:16:44,380 --> 00:16:46,860
It reveals the hidden logic in these systems.

476
00:16:46,900 --> 00:16:48,980
And when you think about the possibilities,

477
00:16:48,980 --> 00:16:51,460
being able to precisely engineer proteins

478
00:16:51,460 --> 00:16:54,380
for specific jobs, I mean, designing medicines

479
00:16:54,380 --> 00:16:56,860
that are more stable and last longer in the body,

480
00:16:56,860 --> 00:16:58,820
creating enzymes that work more efficiently

481
00:16:58,820 --> 00:17:01,020
for sustainable manufacturing,

482
00:17:01,020 --> 00:17:02,220
it feels like we're getting closer

483
00:17:02,220 --> 00:17:04,220
to truly being able to design biology

484
00:17:04,220 --> 00:17:06,020
at its most fundamental level.

485
00:17:06,020 --> 00:17:07,780
It really opens up the imagination.

486
00:17:07,780 --> 00:17:11,060
What other incredibly complex biological puzzles,

487
00:17:11,060 --> 00:17:14,100
beyond protein stability, might we unlock next?

488
00:17:14,100 --> 00:17:15,980
Using these kinds of deep learning tools,

489
00:17:15,980 --> 00:17:17,780
what's the next big domino to fall?

490
00:17:17,780 --> 00:17:19,740
That's the exciting question, isn't it?

491
00:17:19,740 --> 00:17:21,940
The potential seems enormous.
