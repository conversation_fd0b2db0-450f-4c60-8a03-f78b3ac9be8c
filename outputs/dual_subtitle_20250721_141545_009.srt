1
00:00:00,000 --> 00:00:10,000
你有没有停止思考如何这些小小的完全不明显的变化发生在维生素层层层内的变化
Have you ever stopped to think about how these tiny, almost imperceptible changes occur within the layers of vitamins

2
00:00:10,000 --> 00:00:17,600
特别是如何这些小小的变化可以有很大的影响在健康、医疗甚至企业上
Especially how these small changes can have significant impacts on health, medicine, and even businesses

3
00:00:17,600 --> 00:00:18,440
这种变化是很惊人的
This kind of change is truly astonishing

4
00:00:18,440 --> 00:00:19,240
确实是
Indeed it is

5
00:00:19,240 --> 00:00:21,320
基因均衡,那就是垃圾
Genetic equilibrium, that's nonsense

6
00:00:21,320 --> 00:00:24,000
它的形状是重要的
Its shape is what matters

7
00:00:24,000 --> 00:00:24,560
正确的
Correct

8
00:00:24,560 --> 00:00:32,400
更重要的是,连一个氨酸的变化,就一个小小的建筑物层层层都可以把这种稳定性放弃
More importantly, even a single amino acid change, just a tiny building block, can disrupt this stability

9
00:00:32,400 --> 00:00:35,200
对,完全改变它的功能,或者缺乏它
Yes, completely altering its function or rendering it ineffective

10
00:00:35,200 --> 00:00:42,080
所以我们要如何预测这些变化,以及重要的是,它们的后果
So how can we predict these changes and, more importantly, their consequences

11
00:00:42,080 --> 00:00:43,360
这件事实在是很难解决
This issue is incredibly difficult to solve

12
00:00:43,360 --> 00:00:45,720
这就是我们今天要解决的问题
This is the problem we aim to tackle today

13
00:00:45,720 --> 00:00:49,680
这次深入的探索集中了一个非常有趣的新研究文章
This in-depth exploration focuses on a fascinating new research paper

14
00:00:49,680 --> 00:00:52,560
它介绍了一个计算方法叫DP-STAB
It introduces a computational method called DP-STAB

15
00:00:52,560 --> 00:00:53,600
DP-STAB,对
DP-STAB, yes

16
00:00:53,600 --> 00:00:54,440
我们将探索
We will explore

17
00:00:54,440 --> 00:00:55,640
如何这种新的方法
How this new method

18
00:00:55,640 --> 00:00:57,560
终于能够破坏
Can finally break through

19
00:00:57,560 --> 00:01:01,560
这是一项医学科技的非常重要的,复杂的挑战
This is a highly important and complex challenge in medical technology

20
00:01:01,560 --> 00:01:03,440
是的,我们的任务是
Yes, our mission is

21
00:01:03,440 --> 00:01:05,560
为您解决DP-STAB
Solving DP-STAB for you

22
00:01:05,560 --> 00:01:07,600
不管您正在准备会议
Whether you're preparing for a meeting

23
00:01:07,600 --> 00:01:10,080
或者只是有兴趣的关于最新的科学
Or simply curious about the latest science

24
00:01:10,080 --> 00:01:13,000
或者可能只是有兴趣的关于AI的改变
Or perhaps just interested in AI-driven changes

25
00:01:13,000 --> 00:01:14,000
正是
Precisely

26
00:01:14,000 --> 00:01:14,800
我们想给您展示
We want to show you

27
00:01:14,800 --> 00:01:16,600
DP-STAB的效果
The effects of DP-STAB

28
00:01:16,600 --> 00:01:18,760
这些单个变化的均衡
The equilibrium of these individual changes

29
00:01:18,760 --> 00:01:21,000
并重要的是,为什么它实际上重要
And importantly, why it truly matters

30
00:01:21,000 --> 00:01:23,360
因为这不只是学术的疑问
Because this isn't just an academic question

31
00:01:23,360 --> 00:01:24,000
并不是
It's not

32
00:01:24,000 --> 00:01:27,000
这是一个有真实的可能性的破坏
This is a disruption with real potential

33
00:01:27,000 --> 00:01:31,480
对于医疗、工业、甚至更多的绿色企业来说
For healthcare, industry, and even greener businesses

34
00:01:31,480 --> 00:01:34,400
好的,我们在谈谈细微细的细胞改变
Well, we're talking about minute cellular changes

35
00:01:34,400 --> 00:01:35,840
巨大的影响
Massive impact

36
00:01:35,840 --> 00:01:38,320
我们在DP-STAB的解决前
Before solving DP-STAB

37
00:01:38,320 --> 00:01:41,000
我们先集中在问题上
Let's first focus on the problem

38
00:01:41,000 --> 00:01:44,960
为什么这种改变是如此难以预测的
Why are these changes so hard to predict?

39
00:01:44,960 --> 00:01:47,120
是的,这就是奥特吉与达特尼姆的价值
Yes, this is where Autti and Dartnium's value lies

40
00:01:47,120 --> 00:01:50,080
对,达特尼姆是吉布免费能量的改变
Right, Dartnium is the change in Gibbs free energy

41
00:01:50,080 --> 00:01:52,040
是一个稳定的改变
is a stable change

42
00:01:52,040 --> 00:01:53,960
而DP-STAB是烧焦的改变
while DP-STAB is a scorched change

43
00:01:54,000 --> 00:01:57,560
知道这些改变是针对蛋白工程的烧焦
knowing these changes are scorched for protein engineering

44
00:01:57,560 --> 00:01:58,560
或者设计新的药物
or designing new drugs

45
00:01:58,560 --> 00:01:59,640
完全不值得
completely not worth it

46
00:01:59,640 --> 00:02:01,240
如果你想要使蛋白更稳定
if you want to make proteins more stable

47
00:02:01,240 --> 00:02:03,440
或者设计正确的药物
or design the right drugs

48
00:02:03,440 --> 00:02:04,560
你必须要预测这些东西
you must predict these things

49
00:02:04,560 --> 00:02:06,920
但是,是的,正确性是一个巨大的障碍
but, yes, accuracy is a huge obstacle

50
00:02:06,920 --> 00:02:08,880
那么,什么是以前的试验方法
so, what were the previous experimental methods

51
00:02:08,880 --> 00:02:11,960
历史上,有两个主要的路线
historically, there were two main approaches

52
00:02:11,960 --> 00:02:14,800
首先,有设计基础的方法
first, there are design-based methods

53
00:02:14,800 --> 00:02:15,560
好的,很合理
okay, very reasonable

54
00:02:15,560 --> 00:02:16,680
你看看3D的设计
you look at the 3D design

55
00:02:16,680 --> 00:02:20,720
对,你需要一个高品质的细腻3D图表
yes, you need a high-quality detailed 3D model

56
00:02:20,720 --> 00:02:22,240
的蛋白
of the protein

57
00:02:22,240 --> 00:02:23,840
需要正确的绿色线路线
requires the correct green line route

58
00:02:24,000 --> 00:02:25,280
和一个引擎的线路线
and an engine's line route

59
00:02:25,280 --> 00:02:28,240
去看到改变一个小部分的部分有什么影响
to see how changing a small part affects the whole

60
00:02:28,240 --> 00:02:29,640
很合理,那是什么捷迹
very reasonable, what's the shortcut

61
00:02:29,640 --> 00:02:31,880
捷迹就是得到那个绿色线路线
The shortcut is to get that green route

62
00:02:31,880 --> 00:02:32,960
高品质的细腻
High-quality delicacy

63
00:02:32,960 --> 00:02:34,360
通常是很困难
Usually very difficult

64
00:02:34,360 --> 00:02:36,480
甚至是不可能得到的
Even impossible to obtain

65
00:02:36,480 --> 00:02:37,400
所以这些方法
So these methods

66
00:02:37,400 --> 00:02:40,200
在许多许多情况下并不适合
Are unsuitable in many, many cases

67
00:02:40,200 --> 00:02:42,080
没有细腻,没有预测
No delicacy, no prediction

68
00:02:42,080 --> 00:02:43,120
好的,那是限制的
Well, that's the limitation

69
00:02:43,120 --> 00:02:44,080
那是什么选择
What is the alternative

70
00:02:44,080 --> 00:02:45,400
另一个主要的方法是
Another major approach is

71
00:02:45,400 --> 00:02:47,040
系统基础的方法
System-based methods

72
00:02:47,040 --> 00:02:49,600
这些方法比较适合大规模的东西
These methods are more suitable for large-scale things

73
00:02:49,600 --> 00:02:50,560
因为你只需要
Because you only need

74
00:02:50,560 --> 00:02:52,680
蛋白酵素系统的细腻
The delicacy of the protein-enzyme system

75
00:02:52,680 --> 00:02:53,880
就是一个细腻
Just a delicacy

76
00:02:53,880 --> 00:02:55,160
基本的研究
Fundamental research

77
00:02:55,160 --> 00:02:57,680
对,我们有很多细腻的数据
Yes, we have a lot of delicate data

78
00:02:57,680 --> 00:02:58,840
比较容易得到
Relatively easy to obtain

79
00:02:58,840 --> 00:03:00,320
但他们困难了,为什么
But they are difficult, why

80
00:03:00,320 --> 00:03:01,480
他们困难了,是的
They are difficult, yes

81
00:03:01,480 --> 00:03:02,600
因为基本上
Because basically

82
00:03:02,600 --> 00:03:04,440
他们不能看到
They cannot see

83
00:03:04,440 --> 00:03:05,640
细腻的改变
Subtle changes

84
00:03:05,640 --> 00:03:06,840
在区域中
In the region

85
00:03:06,840 --> 00:03:08,080
在变化的地方
In the changing areas

86
00:03:08,080 --> 00:03:08,800
啊,好的
Ah, okay

87
00:03:08,800 --> 00:03:10,720
所以即使整个细腻
So even if the entire subtlety

88
00:03:10,720 --> 00:03:12,480
不改变形状
Doesn't change shape

89
00:03:12,480 --> 00:03:14,880
对,那些细腻的改变
Yes, those subtle changes

90
00:03:14,880 --> 00:03:17,160
和在附近的互动
And interactions nearby

91
00:03:17,160 --> 00:03:19,360
这些是比较坚定的
These are more robust

92
00:03:19,360 --> 00:03:20,800
而细腻方法
While subtle methods

93
00:03:20,800 --> 00:03:21,680
他们只是不能
They simply cannot

94
00:03:21,680 --> 00:03:23,440
能够实际地捕捉这些细腻
Actually capture these subtleties

95
00:03:23,440 --> 00:03:23,840
他们错过了
They missed

96
00:03:23,840 --> 00:03:25,960
在当地的细腻中
In the local subtleties

97
00:03:25,960 --> 00:03:26,920
还有另一个问题
There's another issue

98
00:03:26,920 --> 00:03:27,360
对吧
Right

99
00:03:27,360 --> 00:03:29,040
有关数据的问题
Issues related to data

100
00:03:29,040 --> 00:03:30,360
哦,是的
Oh, yes

101
00:03:30,360 --> 00:03:31,640
一个大问题
A big issue

102
00:03:31,640 --> 00:03:32,640
数据不平衡
Data imbalance

103
00:03:32,640 --> 00:03:33,080
意思是
It means

104
00:03:33,080 --> 00:03:34,160
当我们看
When we look at

105
00:03:34,160 --> 00:03:36,360
我们的测试数据时
Our test data

106
00:03:36,360 --> 00:03:39,600
细腻变化的变化
Fine-grained changes

107
00:03:39,600 --> 00:03:41,680
比变化的变化
Than coarse changes

108
00:03:41,680 --> 00:03:43,640
更常见
More common

109
00:03:43,640 --> 00:03:44,760
这就是你经常想要的
This is often what you want

110
00:03:44,760 --> 00:03:45,680
对吧
Right

111
00:03:45,680 --> 00:03:46,800
一个更坚定的细腻
A more robust fine-grained

112
00:03:46,800 --> 00:03:48,480
对,所以数据
Yes, so the data

113
00:03:48,480 --> 00:03:50,320
用来训练预测模式
Used to train predictive models

114
00:03:50,320 --> 00:03:51,400
对细腻变化的例子
Examples of fine-grained changes

115
00:03:51,400 --> 00:03:53,160
有很大影响
Has a significant impact

116
00:03:53,160 --> 00:03:53,600
这可以
This can

117
00:03:53,600 --> 00:03:54,560
让模型变得更坚硬
Make the model more rigid

118
00:03:54,560 --> 00:03:55,040
对
Correct

119
00:03:55,040 --> 00:03:56,080
让它们较少
Make them less likely

120
00:03:56,080 --> 00:03:56,680
找到那些
To detect those

121
00:03:56,680 --> 00:03:58,840
珍贵的稳定模式
Precious stable patterns

122
00:03:58,840 --> 00:03:59,800
对
Correct

123
00:03:59,800 --> 00:04:00,920
模型可能只会学习
Models may only learn

124
00:04:00,920 --> 00:04:01,360
通常会预测
Typically predict

125
00:04:01,360 --> 00:04:02,800
变化的变化
Changes of changes

126
00:04:02,800 --> 00:04:03,480
因为那是他们
Because that is what they

127
00:04:03,480 --> 00:04:04,280
经常看到的
Frequently observe

128
00:04:04,280 --> 00:04:05,160
训练数据
Training data

129
00:04:05,160 --> 00:04:05,440
好的
Good

130
00:04:05,440 --> 00:04:05,920
所以我们有
So we have

131
00:04:05,920 --> 00:04:07,000
失去的细腻
Lost subtleties

132
00:04:07,000 --> 00:04:08,200
无法看到
Unable to see

133
00:04:08,200 --> 00:04:09,400
地区变化的方法
Methods for regional variations

134
00:04:09,400 --> 00:04:10,640
还有变化数据
And variation data

135
00:04:10,640 --> 00:04:12,120
这真是一座山
This is truly a mountain

136
00:04:12,120 --> 00:04:13,480
这实际上是为了
This is actually meant to

137
00:04:13,480 --> 00:04:15,600
需要一个新的方法
Require a new approach

138
00:04:15,600 --> 00:04:15,840
好的
Good

139
00:04:15,840 --> 00:04:17,400
所以进入DP步骤
So proceed to the DP step

140
00:04:17,400 --> 00:04:19,000
这里是非常有趣的地方
This is where it gets very interesting

141
00:04:19,000 --> 00:04:20,640
这篇文章提出的
proposed in this article

142
00:04:20,640 --> 00:04:22,480
是一个
is a

143
00:04:22,480 --> 00:04:22,920
新的
new

144
00:04:22,920 --> 00:04:24,640
深入学习
deep learning

145
00:04:24,640 --> 00:04:26,000
系统
system

146
00:04:26,000 --> 00:04:27,400
特别设计
specifically designed

147
00:04:27,400 --> 00:04:28,560
解决我们刚刚
to address all the limitations

148
00:04:28,560 --> 00:04:29,240
提出的所有限制
we just mentioned

149
00:04:29,240 --> 00:04:29,960
这就是目标
That is the goal

150
00:04:29,960 --> 00:04:31,360
要得到你
To achieve what you

151
00:04:31,360 --> 00:04:31,880
预期的
expect

152
00:04:31,880 --> 00:04:33,320
基础基础方法
basic foundational methods

153
00:04:33,320 --> 00:04:34,440
但只用
but using only

154
00:04:34,440 --> 00:04:35,200
系统
the system

155
00:04:35,200 --> 00:04:36,680
使它更加高级
to make it more advanced

156
00:04:36,680 --> 00:04:37,720
那它如何做到的
So how does it do that

157
00:04:37,720 --> 00:04:38,760
那是主要的想法吗
Is that the main idea

158
00:04:38,760 --> 00:04:40,080
主要的创新
The key innovation

159
00:04:40,080 --> 00:04:42,000
是使用一个非常强大的
is using a very powerful

160
00:04:42,000 --> 00:04:43,880
重量语言模式
weighted language model

161
00:04:43,880 --> 00:04:45,200
PLLM

162
00:04:45,200 --> 00:04:46,480
就像AI模式
Just like the AI mode

163
00:04:46,480 --> 00:04:47,360
写文章
Write articles

164
00:04:47,360 --> 00:04:48,520
但为了细腻
But for delicacy

165
00:04:48,520 --> 00:04:49,520
就像这样
Just like this

166
00:04:49,520 --> 00:04:50,320
它被训练在
It was trained in

167
00:04:50,320 --> 00:04:51,160
真正大量的
Truly massive

168
00:04:51,160 --> 00:04:52,720
细腻运行计划中
Delicate operation plans

169
00:04:52,720 --> 00:04:54,600
它学习细腻的语言
It learns delicate language

170
00:04:54,600 --> 00:04:55,520
如何变化
How to vary

171
00:04:55,520 --> 00:04:56,240
细腻细胞
Delicate cells

172
00:04:56,240 --> 00:04:57,040
如何相互联系
How to interconnect

173
00:04:57,040 --> 00:04:57,800
特别是
Especially

174
00:04:57,800 --> 00:04:58,680
DP-STAB使用了
DP-STAB uses

175
00:04:58,680 --> 00:05:00,360
一个叫ESM2的模式
A model called ESM2

176
00:05:00,360 --> 00:05:01,040
ESM2

177
00:05:01,040 --> 00:05:01,600
好的
Okay

178
00:05:01,600 --> 00:05:02,240
我听到过
I've heard of it

179
00:05:02,240 --> 00:05:02,560
是的
Yes

180
00:05:02,560 --> 00:05:03,680
它是一种现实性的模式
It is a realistic model

181
00:05:03,680 --> 00:05:05,240
所以ESM2帮助DP-STAB
So ESM2 assists DP-STAB

182
00:05:05,240 --> 00:05:06,640
理解细腻运行计划
Understand delicate operation plans

183
00:05:06,640 --> 00:05:08,320
但是这里有个聪明的部分
But here's the clever part

184
00:05:08,320 --> 00:05:09,360
它也可以用来
It can also be used

185
00:05:09,360 --> 00:05:10,600
从细腻运行计划
From delicate operation plans

186
00:05:10,600 --> 00:05:11,400
采取证明信息
Extract proof information

187
00:05:11,400 --> 00:05:12,280
证明信息
Proof information

188
00:05:12,280 --> 00:05:13,880
像形状信息
Like shape information

189
00:05:13,880 --> 00:05:14,640
有点
A bit

190
00:05:14,640 --> 00:05:15,720
它可以预测一些
It can predict some

191
00:05:15,720 --> 00:05:17,120
细腻运行计划的图表
Diagrams of delicate operation plans

192
00:05:17,120 --> 00:05:17,600
基本上
Basically

193
00:05:17,600 --> 00:05:18,440
哪些氨酸物
Which amino acids

194
00:05:18,440 --> 00:05:20,480
可能在3D图形中
Might be in 3D graphics

195
00:05:20,480 --> 00:05:21,520
图形图形中
In graphical graphics

196
00:05:21,520 --> 00:05:22,280
相互联系
Interconnected

197
00:05:22,280 --> 00:05:22,760
啊
Ah

198
00:05:22,760 --> 00:05:23,480
所以它是预测
So it predicts

199
00:05:23,480 --> 00:05:25,040
细腻运行计划
Delicate operation plans

200
00:05:25,040 --> 00:05:25,960
不需要
Without needing

201
00:05:25,960 --> 00:05:27,640
实际的试验组织
Actual experiment organization

202
00:05:27,640 --> 00:05:28,320
对
Correct

203
00:05:28,320 --> 00:05:28,840
它是
It is

204
00:05:28,840 --> 00:05:32,160
不仅是在看
Not just looking at

205
00:05:32,160 --> 00:05:33,520
细腻运行的图形
Delicately running graphics

206
00:05:33,520 --> 00:05:34,120
而是在看
But rather observing

207
00:05:34,120 --> 00:05:35,680
细腻运行的图形
Delicately running graphics

208
00:05:35,680 --> 00:05:36,200
哇
Wow

209
00:05:36,200 --> 00:05:36,960
好的
Okay

210
00:05:36,960 --> 00:05:38,160
很聪明
Very clever

211
00:05:38,160 --> 00:05:38,800
但是你也提到一些
But you also mentioned some

212
00:05:38,800 --> 00:05:40,040
秘密的方法
Secret methods

213
00:05:40,040 --> 00:05:41,280
具体的方法
Specific methods

214
00:05:41,280 --> 00:05:41,520
对
Correct

215
00:05:41,520 --> 00:05:42,840
DP-STAB

216
00:05:42,840 --> 00:05:43,800
有几个重要的东西
There are several important things

217
00:05:43,800 --> 00:05:44,760
非常出色
Exceptionally outstanding

218
00:05:44,760 --> 00:05:45,360
首先
Firstly

219
00:05:45,360 --> 00:05:45,960
是这个
It is this

220
00:05:45,960 --> 00:05:47,760
自动细腻运行计划
Automatic delicate operation plan

221
00:05:47,760 --> 00:05:48,400
的方法
the method

222
00:05:48,400 --> 00:05:49,920
自动细腻运行
automatically fine-grained operation

223
00:05:49,920 --> 00:05:50,920
什么意思
what does it mean

224
00:05:50,920 --> 00:05:51,600
实际上
in fact

225
00:05:51,600 --> 00:05:53,000
它似乎很复杂
it seems complicated

226
00:05:53,000 --> 00:05:53,760
但它是一个
but it is a

227
00:05:53,760 --> 00:05:55,400
教导自己
teach itself

228
00:05:55,400 --> 00:05:56,000
改善
improve

229
00:05:56,000 --> 00:05:56,800
假设
assume

230
00:05:56,800 --> 00:05:57,720
呃
uh

231
00:05:57,720 --> 00:05:58,600
模特做出
the model makes

232
00:05:58,600 --> 00:06:00,560
某些数据的预测
predictions on certain data

233
00:06:00,560 --> 00:06:01,640
然后它使用
then it uses

234
00:06:01,640 --> 00:06:02,520
自己的预测
its own predictions

235
00:06:02,520 --> 00:06:03,840
作为增加训练
as additional training

236
00:06:03,840 --> 00:06:04,920
但只有
but only

237
00:06:04,920 --> 00:06:05,800
自信的
confident

238
00:06:05,800 --> 00:06:06,360
所以它
so it

239
00:06:06,360 --> 00:06:07,320
用自己的出口
uses its own output

240
00:06:07,320 --> 00:06:08,400
细腻运行计划
fine-grained operation plan

241
00:06:08,400 --> 00:06:09,040
差不多
Almost

242
00:06:09,040 --> 00:06:10,160
它增加训练数据
It increases training data

243
00:06:10,160 --> 00:06:11,160
帮助模特学习
Helps the model learn

244
00:06:11,160 --> 00:06:12,400
更多的细腻运行
More nuanced operations

245
00:06:12,400 --> 00:06:13,120
并使它
And makes it

246
00:06:13,120 --> 00:06:13,560
更好地
Better

247
00:06:13,560 --> 00:06:14,320
在细腻运行
In nuanced operations

248
00:06:14,320 --> 00:06:15,160
使用自己的知识
Using its own knowledge

249
00:06:15,160 --> 00:06:16,040
进行新的细腻运行
To perform new nuanced operations

250
00:06:16,040 --> 00:06:17,080
它从未见过
It has never seen

251
00:06:17,080 --> 00:06:17,480
好的
Okay

252
00:06:17,480 --> 00:06:18,080
那是一部分
That's one part

253
00:06:18,080 --> 00:06:18,680
那是另一部分吗
Is that another part?

254
00:06:18,680 --> 00:06:19,600
另一个重要的方法
Another important approach

255
00:06:19,600 --> 00:06:19,800
就是
Is

256
00:06:19,800 --> 00:06:21,160
对比的限制
The constraint of contrast

257
00:06:21,160 --> 00:06:21,920
对比的限制
The constraint of contrast

258
00:06:21,920 --> 00:06:22,200
好的
Okay

259
00:06:22,200 --> 00:06:22,880
把这些都分析下来
Analyze all of this

260
00:06:22,880 --> 00:06:24,760
它根据基本的逻辑
It follows basic logic

261
00:06:25,480 --> 00:06:26,240
如果您把
If you take

262
00:06:26,240 --> 00:06:27,040
一种胃酸
a gastric acid

263
00:06:27,040 --> 00:06:28,000
从A到B
from A to B

264
00:06:28,440 --> 00:06:29,280
变成了
and turn it into

265
00:06:29,280 --> 00:06:30,920
某种程度的稳定
a certain degree of stability

266
00:06:31,360 --> 00:06:31,720
比如说
for example

267
00:06:31,720 --> 00:06:32,440
它减少了
it decreases

268
00:06:32,440 --> 00:06:33,360
两个细腻
two delicate

269
00:06:33,360 --> 00:06:33,920
好的
good

270
00:06:33,920 --> 00:06:34,920
那么逻辑上
then logically

271
00:06:34,920 --> 00:06:36,480
从B到A
from B to A

272
00:06:36,480 --> 00:06:36,880
应该做到
should achieve

273
00:06:36,880 --> 00:06:37,760
相反的事情
the opposite

274
00:06:37,760 --> 00:06:38,000
对吧
right

275
00:06:38,000 --> 00:06:38,280
它应该
it should

276
00:06:38,280 --> 00:06:38,680
减少
reduce

277
00:06:38,680 --> 00:06:39,840
两个细腻运行的稳定
the stability of two delicate operations

278
00:06:39,840 --> 00:06:41,120
很合理
very reasonable

279
00:06:41,120 --> 00:06:41,600
Daiichi

280
00:06:41,600 --> 00:06:41,760
应该
should

281
00:06:41,760 --> 00:06:42,840
翻译为
Translated as

282
00:06:42,840 --> 00:06:43,680
正确
Correct

283
00:06:43,680 --> 00:06:44,520
Daiichi B

284
00:06:44,520 --> 00:06:45,320
应该等于
Should equal

285
00:06:45,320 --> 00:06:47,040
-DBA

286
00:06:47,040 --> 00:06:47,920
但是年轻人的模特
But young people's models

287
00:06:47,920 --> 00:06:49,320
不经常遵循这个规则
Do not often follow this rule

288
00:06:49,320 --> 00:06:50,440
真的吗
Really

289
00:06:50,440 --> 00:06:50,920
这似乎是一个
This seems to be a

290
00:06:50,920 --> 00:06:52,120
基本的权利
Fundamental right

291
00:06:52,120 --> 00:06:53,000
是的
Yes

292
00:06:53,000 --> 00:06:53,920
但是他们的建筑
But their architecture

293
00:06:53,920 --> 00:06:55,360
并没有扩展它
Did not extend it

294
00:06:55,360 --> 00:06:56,000
DP-STAB

295
00:06:56,000 --> 00:06:56,720
明显地
Clearly

296
00:06:56,720 --> 00:06:58,440
建立了这个限制
Established this limitation

297
00:06:58,440 --> 00:06:59,560
它使模特
It makes the models

298
00:06:59,560 --> 00:07:00,680
学习和尊重
Learn and respect

299
00:07:00,680 --> 00:07:01,760
这个基本的
This basic

300
00:07:01,760 --> 00:07:02,880
逻辑
Logic

301
00:07:02,880 --> 00:07:03,440
或者说
Or rather

302
00:07:03,440 --> 00:07:04,360
反逆逻辑
Counter-logic

303
00:07:04,360 --> 00:07:05,000
这让
This makes

304
00:07:05,000 --> 00:07:05,880
预测比较
Predictive comparison

305
00:07:05,880 --> 00:07:06,480
实际
Actual

306
00:07:06,480 --> 00:07:07,200
实际
Actual

307
00:07:07,200 --> 00:07:07,800
和可靠
And reliable

308
00:07:07,800 --> 00:07:08,560
更加稳定
More stable

309
00:07:08,560 --> 00:07:08,840
是的
Yes

310
00:07:08,840 --> 00:07:09,480
它确保
It ensures

311
00:07:09,480 --> 00:07:10,720
内部的适当性
Internal appropriateness

312
00:07:10,720 --> 00:07:11,000
好的
Good

313
00:07:11,000 --> 00:07:11,400
那么我们有
Then we have

314
00:07:11,400 --> 00:07:12,040
PLMM

315
00:07:12,040 --> 00:07:12,960
为选项
As an option

316
00:07:12,960 --> 00:07:13,840
自动解决
Automatic resolution

317
00:07:13,840 --> 00:07:14,920
为了学习更好
To learn better

318
00:07:14,920 --> 00:07:15,280
还有这个
And this

319
00:07:15,280 --> 00:07:16,520
适当逻辑的限制
Limitations of proper logic

320
00:07:16,520 --> 00:07:18,200
为了适当逻辑
For proper logic

321
00:07:18,200 --> 00:07:18,800
我们可以
We can

322
00:07:18,800 --> 00:07:19,320
继续谈谈吗
Continue talking

323
00:07:19,320 --> 00:07:20,160
这些组件
These components

324
00:07:20,160 --> 00:07:20,880
怎么组成的
How they are composed

325
00:07:20,880 --> 00:07:21,480
当然
Of course

326
00:07:21,480 --> 00:07:22,080
DP-STAB

327
00:07:22,080 --> 00:07:22,520
基本上
Basically

328
00:07:22,520 --> 00:07:23,880
有三个主要模组
There are three main modules

329
00:07:23,880 --> 00:07:24,640
在一起
Together

330
00:07:24,640 --> 00:07:25,360
第一个
The first one

331
00:07:25,360 --> 00:07:26,560
是逻辑研究器
Is the logic analyzer

332
00:07:26,560 --> 00:07:27,840
那是ESM2的部分
That's part of ESM2

333
00:07:27,840 --> 00:07:28,280
是的
Yes

334
00:07:28,280 --> 00:07:29,920
是ESM2的功能
It's a function of ESM2

335
00:07:29,920 --> 00:07:30,640
它的工作
Its job

336
00:07:30,640 --> 00:07:31,280
是把
Is to

337
00:07:31,280 --> 00:07:32,400
成分运输
Transport components

338
00:07:32,400 --> 00:07:33,120
运输到
Transport them to

339
00:07:33,120 --> 00:07:33,600
那些
Those

340
00:07:33,600 --> 00:07:33,920
充满
Filled with

341
00:07:33,920 --> 00:07:34,600
生命性的
Vital

342
00:07:34,600 --> 00:07:35,760
资料
Data

343
00:07:35,760 --> 00:07:36,400
并且
And

344
00:07:36,400 --> 00:07:36,840
预测
Predict

345
00:07:36,840 --> 00:07:37,080
那些
Those

346
00:07:37,080 --> 00:07:37,760
联络图
Contact map

347
00:07:37,760 --> 00:07:38,280
我们谈论过
We discussed

348
00:07:38,280 --> 00:07:38,920
选择的
Selected

349
00:07:38,920 --> 00:07:40,040
组织资料
Organize data

350
00:07:40,040 --> 00:07:41,120
这是最初的
This is the initial

351
00:07:41,120 --> 00:07:42,040
计算机
Computer

352
00:07:42,040 --> 00:07:42,440
明白
Understand

353
00:07:42,440 --> 00:07:43,120
接下来呢
What's next

354
00:07:43,120 --> 00:07:43,880
接下来是
Next is

355
00:07:43,880 --> 00:07:44,880
隔离参数研究器
Isolation parameter analyzer

356
00:07:44,880 --> 00:07:45,480
这里是
Here is

357
00:07:45,480 --> 00:07:46,720
在
In

358
00:07:46,720 --> 00:07:47,560
逻辑研究器中
Logic analyzer

359
00:07:47,560 --> 00:07:48,200
专注在
Focus on

360
00:07:48,200 --> 00:07:49,160
当地环境
Local environment

361
00:07:49,160 --> 00:07:49,880
对
Correct

362
00:07:49,880 --> 00:07:50,200
是的
Yes

363
00:07:50,200 --> 00:07:51,320
它使用了
It used

364
00:07:51,320 --> 00:07:52,400
逻辑研究器的
The logic analyzer's

365
00:07:52,400 --> 00:07:53,480
选择方式
Selection method

366
00:07:53,480 --> 00:07:54,280
想起
Recall

367
00:07:54,280 --> 00:07:55,240
给模组
To the module

368
00:07:55,240 --> 00:07:55,880
能够
Can

369
00:07:55,880 --> 00:07:57,000
专注在
Focus on

370
00:07:57,000 --> 00:07:58,200
逻辑研究器
Logic analyzer

371
00:07:58,200 --> 00:07:58,480
和
And

372
00:07:58,480 --> 00:07:58,920
其重要的
Its important

373
00:07:58,920 --> 00:07:59,440
邻居
Neighbors

374
00:07:59,440 --> 00:07:59,960
的
Of

375
00:07:59,960 --> 00:08:00,560
能够
Able to

376
00:08:00,560 --> 00:08:00,880
猜测
Guess

377
00:08:00,880 --> 00:08:01,320
旁边的
Adjacent

378
00:08:01,320 --> 00:08:01,760
逻辑
Logic

379
00:08:01,760 --> 00:08:01,920
是
Is

380
00:08:01,920 --> 00:08:02,240
最
The most

381
00:08:02,240 --> 00:08:02,840
重要的
Important

382
00:08:02,840 --> 00:08:03,120
并且
And

383
00:08:03,120 --> 00:08:03,680
从它
From it

384
00:08:03,680 --> 00:08:03,720
进行
Conduct

385
00:08:03,720 --> 00:08:04,520
讯息
Message

386
00:08:04,520 --> 00:08:04,920
是的
Yes

387
00:08:04,920 --> 00:08:05,800
具体性
Specificity

388
00:08:05,800 --> 00:08:06,440
它也可以
It can also

389
00:08:06,440 --> 00:08:07,000
掌握
Master

390
00:08:07,000 --> 00:08:08,720
长远的产生
Long-term generation

391
00:08:08,720 --> 00:08:09,200
有时候
Sometimes

392
00:08:09,200 --> 00:08:09,720
逻辑研究器
Logic researcher

393
00:08:09,720 --> 00:08:10,320
在
In

394
00:08:10,320 --> 00:08:10,840
连续
Continuous

395
00:08:10,840 --> 00:08:11,120
中
Middle

396
00:08:11,120 --> 00:08:11,360
都
All

397
00:08:11,360 --> 00:08:11,960
合成
Synthesize

398
00:08:11,960 --> 00:08:12,080
并
And

399
00:08:12,080 --> 00:08:12,520
互动
Interaction

400
00:08:12,520 --> 00:08:12,640
在
At

401
00:08:12,640 --> 00:08:12,960
3D

402
00:08:12,960 --> 00:08:13,920
空间
Space

403
00:08:13,920 --> 00:08:14,160
这个
This

404
00:08:14,160 --> 00:08:14,600
模组
Module

405
00:08:14,600 --> 00:08:14,840
帮助
Helps

406
00:08:14,840 --> 00:08:15,280
掌握
Master

407
00:08:15,280 --> 00:08:15,520
那些
Those

408
00:08:15,520 --> 00:08:16,120
重要的
Important

409
00:08:16,120 --> 00:08:16,480
非
Non

410
00:08:16,480 --> 00:08:16,800
地区
Region

411
00:08:16,800 --> 00:08:17,360
效果
Effects

412
00:08:17,360 --> 00:08:17,600
那些
Those

413
00:08:17,600 --> 00:08:18,080
更简单的
Simpler

414
00:08:18,080 --> 00:08:18,480
方法
Methods

415
00:08:18,480 --> 00:08:18,880
经常
Often

416
00:08:18,880 --> 00:08:19,280
错失
Miss

417
00:08:19,280 --> 00:08:19,560
好
Good

418
00:08:19,560 --> 00:08:19,760
那么
Then

419
00:08:19,760 --> 00:08:20,760
逻辑研究器
Logic Analyzer

420
00:08:20,760 --> 00:08:21,160
然后
Then

421
00:08:21,160 --> 00:08:21,680
专注在
Focus on

422
00:08:21,680 --> 00:08:22,200
旧地
Old place

423
00:08:22,200 --> 00:08:22,360
和
And

424
00:08:22,360 --> 00:08:22,960
长远的
Long-term

425
00:08:22,960 --> 00:08:23,520
资料
Data

426
00:08:23,520 --> 00:08:24,480
最后的
Final

427
00:08:24,480 --> 00:08:25,480
步骤是
The step is

428
00:08:25,480 --> 00:08:25,920
模组
Module

429
00:08:25,920 --> 00:08:27,080
转移
Transfer

430
00:08:27,080 --> 00:08:27,320
这个
This

431
00:08:27,320 --> 00:08:27,680
模组
Module

432
00:08:27,680 --> 00:08:28,000
取得
Obtain

433
00:08:28,000 --> 00:08:28,280
所有
All

434
00:08:28,280 --> 00:08:28,360
的
Of

435
00:08:28,360 --> 00:08:28,920
资料
Data

436
00:08:28,920 --> 00:08:29,480
包括
Including

437
00:08:29,480 --> 00:08:30,080
原来的
Original

438
00:08:30,080 --> 00:08:30,800
核酸
Nucleic acid

439
00:08:30,800 --> 00:08:31,760
生物
Organism

440
00:08:31,760 --> 00:08:31,960
和
And

441
00:08:31,960 --> 00:08:32,680
变化的
changing

442
00:08:32,680 --> 00:08:32,960
模组
module

443
00:08:32,960 --> 00:08:33,760
组成
composition

444
00:08:33,760 --> 00:08:34,040
对
to

445
00:08:34,040 --> 00:08:34,280
它
it

446
00:08:34,280 --> 00:08:35,080
从
from

447
00:08:35,080 --> 00:08:35,800
两个
two

448
00:08:35,800 --> 00:08:36,400
的
of

449
00:08:36,400 --> 00:08:37,000
功能
function

450
00:08:37,000 --> 00:08:37,360
组成
compose

451
00:08:37,360 --> 00:08:37,960
环境
environment

452
00:08:37,960 --> 00:08:38,520
状态
state

453
00:08:38,520 --> 00:08:38,720
例如
for example

454
00:08:38,720 --> 00:08:39,200
氧气
oxygen

455
00:08:39,200 --> 00:08:39,320
和
and

456
00:08:39,320 --> 00:08:39,800
温度
temperature

457
00:08:39,800 --> 00:08:40,440
尤其
especially

458
00:08:40,440 --> 00:08:40,560
为
for

459
00:08:40,560 --> 00:08:40,920
长远的
long-term

460
00:08:40,920 --> 00:08:41,560
预测
prediction

461
00:08:41,560 --> 00:08:41,840
因为
Because

462
00:08:41,840 --> 00:08:42,320
坚定性
Firmness

463
00:08:42,320 --> 00:08:42,760
也可以
Can also

464
00:08:42,760 --> 00:08:42,880
依据
According to

465
00:08:42,880 --> 00:08:43,080
这些
These

466
00:08:43,080 --> 00:08:43,920
因素
Factors

467
00:08:43,920 --> 00:08:44,320
然后
Then

468
00:08:44,320 --> 00:08:44,440
它
It

469
00:08:44,440 --> 00:08:44,880
取得
Obtain

470
00:08:44,880 --> 00:08:45,280
最后的
Final

471
00:08:45,280 --> 00:08:46,200
预测
Prediction

472
00:08:46,200 --> 00:08:47,440
长远的
Long-term

473
00:08:47,440 --> 00:08:47,680
或
Or

474
00:08:47,680 --> 00:08:48,560
帮助
Help

475
00:08:48,560 --> 00:08:49,400
正确
Correct

476
00:08:49,400 --> 00:08:49,600
它
It

477
00:08:49,600 --> 00:08:50,320
综合
Comprehensive

478
00:08:50,320 --> 00:08:50,920
所有
All

479
00:08:50,920 --> 00:08:50,960
要
Need to

480
00:08:50,960 --> 00:08:51,240
做
Do

481
00:08:51,240 --> 00:08:51,920
最终的
final

482
00:08:51,920 --> 00:08:53,160
坚定性
firmness

483
00:08:53,160 --> 00:08:53,640
改变
change

484
00:08:53,640 --> 00:08:53,880
好的
good

485
00:08:53,880 --> 00:08:54,080
那
that

486
00:08:54,080 --> 00:08:54,320
很
very

487
00:08:54,320 --> 00:08:54,880
详细
detailed

488
00:08:54,880 --> 00:08:55,760
的
of

489
00:08:55,760 --> 00:08:56,280
那么
then

490
00:08:56,280 --> 00:08:56,760
这个
this

491
00:08:56,760 --> 00:08:57,760
万亿美元的
trillion-dollar

492
00:08:57,760 --> 00:08:58,320
问题
question

493
00:08:58,320 --> 00:08:58,360
是
is

494
00:08:58,360 --> 00:08:59,080
它
it

495
00:08:59,080 --> 00:08:59,560
实际上
actually

496
00:08:59,560 --> 00:09:00,400
有多好
how good

497
00:09:00,400 --> 00:09:00,880
这些
these

498
00:09:00,880 --> 00:09:01,480
组成的
composed

499
00:09:01,480 --> 00:09:01,920
组成
composition

500
00:09:01,920 --> 00:09:02,320
有多
how much

501
00:09:02,320 --> 00:09:02,920
复杂
complex

502
00:09:02,920 --> 00:09:03,000
对
correct

503
00:09:03,000 --> 00:09:03,640
终于
finally

504
00:09:03,640 --> 00:09:04,200
有
have

505
00:09:04,200 --> 00:09:04,720
很
very

506
00:09:04,720 --> 00:09:05,240
强劲的
strong

507
00:09:05,240 --> 00:09:05,840
结果
result

508
00:09:05,840 --> 00:09:06,160
DP

509
00:09:06,160 --> 00:09:06,640
STAB

510
00:09:06,640 --> 00:09:07,080
似乎
seems

511
00:09:07,080 --> 00:09:07,520
正在
is

512
00:09:07,520 --> 00:09:07,680
设立
setting up

513
00:09:07,680 --> 00:09:08,200
一个
a

514
00:09:08,200 --> 00:09:08,680
新的
new

515
00:09:08,680 --> 00:09:08,920
状态
status

516
00:09:08,920 --> 00:09:09,320
在
on

517
00:09:09,320 --> 00:09:09,640
整个
entire

518
00:09:09,640 --> 00:09:09,680
图表上
chart

519
00:09:09,680 --> 00:09:10,040
对
towards

520
00:09:10,040 --> 00:09:10,640
对
towards

521
00:09:10,640 --> 00:09:11,120
我们先谈谈
Let's talk first

522
00:09:11,120 --> 00:09:11,360
AG

523
00:09:11,360 --> 00:09:12,160
预测
Prediction

524
00:09:12,160 --> 00:09:12,360
那个
That

525
00:09:12,360 --> 00:09:12,960
变化
Change

526
00:09:12,960 --> 00:09:13,520
的
Of

527
00:09:13,520 --> 00:09:13,560
数据
Data

528
00:09:13,560 --> 00:09:13,920
DP

529
00:09:13,920 --> 00:09:14,320
STAB

530
00:09:14,320 --> 00:09:14,400
的
Of

531
00:09:14,400 --> 00:09:15,240
预测
Prediction

532
00:09:15,240 --> 00:09:15,640
比
Than

533
00:09:15,640 --> 00:09:16,240
之前的
Previous

534
00:09:16,240 --> 00:09:17,080
方法
Method

535
00:09:17,080 --> 00:09:18,040
更低
Lower

536
00:09:18,040 --> 00:09:18,480
低
Low

537
00:09:18,480 --> 00:09:18,640
的
Of

538
00:09:18,640 --> 00:09:19,040
预测
Prediction

539
00:09:19,040 --> 00:09:19,640
意味着
Means

540
00:09:19,640 --> 00:09:19,920
更
More

541
00:09:19,920 --> 00:09:20,440
准确
accurate

542
00:09:20,440 --> 00:09:20,480
更
more

543
00:09:20,480 --> 00:09:21,320
准确
accurate

544
00:09:21,320 --> 00:09:22,360
RMSE

545
00:09:22,360 --> 00:09:22,640
是
is

546
00:09:22,640 --> 00:09:23,440
一个
a

547
00:09:23,440 --> 00:09:23,880
平均
mean

548
00:09:23,880 --> 00:09:24,600
错误
error

549
00:09:24,600 --> 00:09:24,960
下降
decreases

550
00:09:24,960 --> 00:09:25,560
低于
below

551
00:09:25,560 --> 00:09:26,880
0.93

552
00:09:26,880 --> 00:09:27,720
K

553
00:09:27,720 --> 00:09:28,800
MAE

554
00:09:28,800 --> 00:09:29,200
另一个
another

555
00:09:29,200 --> 00:09:29,480
错误
error

556
00:09:29,480 --> 00:09:29,800
的
of

557
00:09:29,800 --> 00:09:29,840
预测
prediction

558
00:09:29,840 --> 00:09:29,960
是
is

559
00:09:29,960 --> 00:09:30,280
低于
below

560
00:09:30,280 --> 00:09:31,200
0.68

561
00:09:31,200 --> 00:09:31,840
K

562
00:09:31,840 --> 00:09:32,440
低于
Below

563
00:09:32,440 --> 00:09:32,560
是
Is

564
00:09:32,560 --> 00:09:33,160
更好的
Better

565
00:09:33,160 --> 00:09:34,320
预测
Prediction

566
00:09:34,320 --> 00:09:34,640
比
Than

567
00:09:34,640 --> 00:09:35,080
实际
Actual

568
00:09:35,080 --> 00:09:36,320
测试
Test

569
00:09:36,320 --> 00:09:36,640
非常
Very

570
00:09:36,640 --> 00:09:37,160
好
Good

571
00:09:37,160 --> 00:09:37,880
预测
Prediction

572
00:09:37,880 --> 00:09:38,600
的
Of

573
00:09:38,600 --> 00:09:38,640
比例
Proportion

574
00:09:38,640 --> 00:09:39,200
非常
Very

575
00:09:39,200 --> 00:09:39,560
高
High

576
00:09:39,560 --> 00:09:39,880
预测
Prediction

577
00:09:39,880 --> 00:09:40,440
比例
Proportion

578
00:09:40,440 --> 00:09:40,880
和
And

579
00:09:40,880 --> 00:09:41,520
实际
Actual

580
00:09:41,520 --> 00:09:41,640
比例
Proportion

581
00:09:41,640 --> 00:09:42,120
非常
Very

582
00:09:42,120 --> 00:09:42,720
高
High

583
00:09:42,720 --> 00:09:43,280
SEC

584
00:09:43,280 --> 00:09:43,400
的
Of

585
00:09:43,400 --> 00:09:43,800
Sperman

586
00:09:43,800 --> 00:09:44,360
预测
Prediction

587
00:09:44,360 --> 00:09:44,560
达到
Reached

588
00:09:44,560 --> 00:09:45,720
0.86

589
00:09:45,720 --> 00:09:46,840
PCC

590
00:09:46,840 --> 00:09:46,960
的
Of

591
00:09:46,960 --> 00:09:47,520
Piercing

592
00:09:47,520 --> 00:09:48,200
预测
Prediction

593
00:09:48,200 --> 00:09:48,560
达到
Reached

594
00:09:48,560 --> 00:09:49,840
0.84PCC
0.84 PCC

595
00:09:49,840 --> 00:09:50,240
低于
Below

596
00:09:50,240 --> 00:09:50,600
1是
1 is

597
00:09:50,600 --> 00:09:50,840
更好
Better

598
00:09:50,840 --> 00:09:50,960
所以
So

599
00:09:50,960 --> 00:09:51,320
那些
Those

600
00:09:51,320 --> 00:09:51,360
是
Are

601
00:09:51,360 --> 00:09:51,760
很棒的
Awesome

602
00:09:51,760 --> 00:09:52,200
预测
Prediction

603
00:09:52,200 --> 00:09:52,680
哇
Wow

604
00:09:52,680 --> 00:09:52,920
好的
Okay

605
00:09:52,920 --> 00:09:53,120
那是
That is

606
00:09:53,120 --> 00:09:54,120
继续的
Ongoing

607
00:09:54,120 --> 00:09:54,920
价值
Value

608
00:09:54,920 --> 00:09:55,120
什么
What

609
00:09:55,120 --> 00:09:55,400
关于
About

610
00:09:55,400 --> 00:09:56,440
级别
Level

611
00:09:56,440 --> 00:09:56,480
的
Of

612
00:09:56,480 --> 00:09:57,000
预测
Prediction

613
00:09:57,000 --> 00:09:57,200
例如
For example

614
00:09:57,200 --> 00:09:57,440
是
Is

615
00:09:57,440 --> 00:09:58,560
稳定
Stable

616
00:09:58,560 --> 00:10:00,040
不稳定
Unstable

617
00:10:00,040 --> 00:10:00,200
还是
Or

618
00:10:00,200 --> 00:10:00,920
平均
Average

619
00:10:00,920 --> 00:10:01,680
这
This

620
00:10:01,680 --> 00:10:01,720
是
Is

621
00:10:01,720 --> 00:10:02,680
你
you

622
00:10:02,680 --> 00:10:02,880
想
want

623
00:10:02,880 --> 00:10:03,160
回答的
to answer

624
00:10:03,160 --> 00:10:03,720
实际
actual

625
00:10:03,720 --> 00:10:03,920
问题
question

626
00:10:03,920 --> 00:10:04,080
而
while

627
00:10:04,080 --> 00:10:04,640
这里
here

628
00:10:04,640 --> 00:10:04,680
也有
also has

629
00:10:04,680 --> 00:10:04,920
DP

630
00:10:04,920 --> 00:10:05,280
Stab

631
00:10:05,280 --> 00:10:05,800
做得
performs

632
00:10:05,800 --> 00:10:06,440
非常
very

633
00:10:06,440 --> 00:10:06,680
好
well

634
00:10:06,680 --> 00:10:06,880
它的
its

635
00:10:06,880 --> 00:10:07,560
级别
level

636
00:10:07,560 --> 00:10:08,000
准确
accuracy

637
00:10:08,000 --> 00:10:08,320
是
is

638
00:10:08,320 --> 00:10:08,640
超过
exceeds

639
00:10:08,640 --> 00:10:09,920
74%

640
00:10:09,920 --> 00:10:10,200
所以
so

641
00:10:10,200 --> 00:10:10,800
约三
About three

642
00:10:10,800 --> 00:10:11,160
四
Four

643
00:10:11,160 --> 00:10:11,640
次
Times

644
00:10:11,640 --> 00:10:11,800
它
It

645
00:10:11,800 --> 00:10:12,320
正确
Correct

646
00:10:12,320 --> 00:10:12,840
显示
Display

647
00:10:12,840 --> 00:10:14,480
无论是否有变化
Whether there is a change or not

648
00:10:14,480 --> 00:10:15,400
弱化
Weaken

649
00:10:15,400 --> 00:10:15,600
或
Or

650
00:10:15,600 --> 00:10:16,120
不影响
Does not affect

651
00:10:16,120 --> 00:10:16,640
蛋白质
Protein

652
00:10:16,640 --> 00:10:17,280
对
To

653
00:10:17,280 --> 00:10:18,080
在他们的测试中
In their tests

654
00:10:18,080 --> 00:10:18,640
它正确地
It correctly

655
00:10:18,640 --> 00:10:19,280
列出了
Listed

656
00:10:19,280 --> 00:10:20,280
684

657
00:10:20,280 --> 00:10:20,440
除了
Except

658
00:10:20,440 --> 00:10:21,440
922

659
00:10:21,440 --> 00:10:21,480
的
Of

660
00:10:21,480 --> 00:10:21,960
情况
Case

661
00:10:21,960 --> 00:10:22,120
这是
This is

662
00:10:22,120 --> 00:10:22,560
很大的
a significant

663
00:10:22,560 --> 00:10:23,480
改善
improvement

664
00:10:23,480 --> 00:10:23,720
而且
and

665
00:10:23,720 --> 00:10:24,440
非常有用
very useful

666
00:10:24,440 --> 00:10:25,280
在
in

667
00:10:25,280 --> 00:10:25,800
可能的
possible

668
00:10:25,800 --> 00:10:26,440
变化
changes

669
00:10:26,440 --> 00:10:26,560
在
in

670
00:10:26,560 --> 00:10:27,320
药物设计
drug design

671
00:10:27,320 --> 00:10:27,720
或
or

672
00:10:27,720 --> 00:10:28,720
氧化工程
oxidation engineering

673
00:10:28,720 --> 00:10:29,240
削弱了
weakened

674
00:10:29,240 --> 00:10:29,840
医疗工作
medical work

675
00:10:29,840 --> 00:10:30,720
我
I

676
00:10:30,720 --> 00:10:31,120
想
want

677
00:10:31,120 --> 00:10:31,160
的
the

678
00:10:31,160 --> 00:10:31,840
重点
focus

679
00:10:31,840 --> 00:10:32,160
省下
save

680
00:10:32,160 --> 00:10:32,400
时间
time

681
00:10:32,400 --> 00:10:32,480
和
and

682
00:10:32,480 --> 00:10:33,320
资源
resource

683
00:10:33,320 --> 00:10:33,600
你
you

684
00:10:33,600 --> 00:10:34,080
也提到
also mentioned

685
00:10:34,080 --> 00:10:34,360
团队
team

686
00:10:34,360 --> 00:10:34,400
的
of

687
00:10:34,400 --> 00:10:35,080
预测
prediction

688
00:10:35,080 --> 00:10:35,520
溶化
melt

689
00:10:35,520 --> 00:10:36,160
温度
temperature

690
00:10:36,160 --> 00:10:36,760
是的
yes

691
00:10:36,760 --> 00:10:37,720
在那里的表现
performance there

692
00:10:37,720 --> 00:10:38,720
也被
is also

693
00:10:38,720 --> 00:10:39,840
认为是最强的
considered the strongest

694
00:10:39,840 --> 00:10:40,000
或
or

695
00:10:40,000 --> 00:10:40,360
至少
at least

696
00:10:40,360 --> 00:10:41,040
相比较好的
relatively better

697
00:10:41,040 --> 00:10:41,560
最佳
best

698
00:10:41,560 --> 00:10:41,960
存在的
existing

699
00:10:41,960 --> 00:10:42,320
方法
method

700
00:10:42,320 --> 00:10:42,440
所以
so

701
00:10:42,440 --> 00:10:43,200
它在
It is in

702
00:10:43,200 --> 00:10:44,160
多层层上
multiple layers

703
00:10:44,160 --> 00:10:44,600
我们能看到
we can see

704
00:10:44,600 --> 00:10:44,720
它在
it in

705
00:10:44,720 --> 00:10:45,080
行动上吗
action

706
00:10:45,080 --> 00:10:45,280
像
like

707
00:10:45,280 --> 00:10:45,800
具体的
concrete

708
00:10:45,800 --> 00:10:46,320
例子
examples

709
00:10:46,320 --> 00:10:46,640
是的
yes

710
00:10:46,640 --> 00:10:47,080
文件
documents

711
00:10:47,080 --> 00:10:47,520
显示
show

712
00:10:47,520 --> 00:10:48,280
DP

713
00:10:48,280 --> 00:10:48,560
研究
research

714
00:10:48,560 --> 00:10:49,880
每个
each

715
00:10:49,880 --> 00:10:50,680
蛋白质
protein

716
00:10:50,680 --> 00:10:51,000
的
's

717
00:10:51,000 --> 00:10:51,520
效果
effect

718
00:10:51,520 --> 00:10:51,640
是
is

719
00:10:51,640 --> 00:10:52,200
实际
actually

720
00:10:52,200 --> 00:10:52,680
设计
designed

721
00:10:52,680 --> 00:10:53,560
工作
work

722
00:10:53,560 --> 00:10:54,000
相比
compared

723
00:10:54,000 --> 00:10:54,120
于
to

724
00:10:54,120 --> 00:10:54,960
更老的方式
older methods

725
00:10:54,960 --> 00:10:55,120
他们
they

726
00:10:55,120 --> 00:10:55,400
看到
saw

727
00:10:55,400 --> 00:10:55,720
选择
choice

728
00:10:55,720 --> 00:10:56,320
改善
improve

729
00:10:56,320 --> 00:10:56,440
的
of

730
00:10:56,440 --> 00:10:57,640
9%

731
00:10:57,640 --> 00:10:57,760
在
in

732
00:10:57,760 --> 00:10:58,280
SEC

733
00:10:58,280 --> 00:10:59,400
7.5%

734
00:10:59,400 --> 00:10:59,480
在
in

735
00:10:59,480 --> 00:11:00,240
PCC

736
00:11:00,240 --> 00:11:00,360
和
and

737
00:11:00,360 --> 00:11:00,600
几乎
nearly

738
00:11:00,600 --> 00:11:01,360
20%

739
00:11:01,360 --> 00:11:01,480
在
in

740
00:11:01,480 --> 00:11:02,640
蛋白质
protein

741
00:11:02,640 --> 00:11:03,360
证明
Proof

742
00:11:03,360 --> 00:11:03,560
所以
Therefore

743
00:11:03,560 --> 00:11:04,120
它不仅是
It is not only

744
00:11:04,120 --> 00:11:04,880
平均的
Average

745
00:11:04,880 --> 00:11:06,080
它也比较适合
It is also suitable

746
00:11:06,080 --> 00:11:07,200
你可能
You may

747
00:11:07,200 --> 00:11:07,640
在
In

748
00:11:07,640 --> 00:11:07,680
具体的
Specific

749
00:11:07,680 --> 00:11:07,720
情况上
Cases

750
00:11:07,720 --> 00:11:08,600
正确
Correct

751
00:11:08,600 --> 00:11:08,880
他们
They

752
00:11:08,880 --> 00:11:09,160
展示了
Demonstrated

753
00:11:09,160 --> 00:11:09,200
一个
A

754
00:11:09,200 --> 00:11:09,960
试验
Experiment

755
00:11:09,960 --> 00:11:11,520
2PTLA

756
00:11:11,520 --> 00:11:11,800
的
Of

757
00:11:11,800 --> 00:11:12,800
A34G

758
00:11:12,800 --> 00:11:13,960
变化
Variation

759
00:11:13,960 --> 00:11:14,280
DP

760
00:11:14,280 --> 00:11:14,600
研究
Study

761
00:11:14,600 --> 00:11:15,040
预测
Prediction

762
00:11:15,040 --> 00:11:15,120
一个
One

763
00:11:15,120 --> 00:11:15,640
均衡
Equilibrium

764
00:11:15,640 --> 00:11:16,280
减少
Reduction

765
00:11:16,280 --> 00:11:18,200
1.2

766
00:11:18,200 --> 00:11:18,840
kM

767
00:11:18,840 --> 00:11:18,920
在
In

768
00:11:18,920 --> 00:11:19,200
真实
Real

769
00:11:19,200 --> 00:11:19,600
价值
Value

770
00:11:19,600 --> 00:11:20,600
试验价值
Experimental value

771
00:11:20,600 --> 00:11:20,800
是
Is

772
00:11:20,800 --> 00:11:21,720
2.1

773
00:11:21,720 --> 00:11:22,280
kM

774
00:11:22,280 --> 00:11:22,440
所以
So

775
00:11:22,440 --> 00:11:23,360
很近
Very close

776
00:11:23,360 --> 00:11:23,520
但
But

777
00:11:23,520 --> 00:11:24,240
重要的是
Importantly

778
00:11:24,240 --> 00:11:24,760
前面的
Previous

779
00:11:24,760 --> 00:11:25,040
主要
Main

780
00:11:25,040 --> 00:11:25,360
方法
Method

781
00:11:25,360 --> 00:11:25,760
预测
Predict

782
00:11:25,760 --> 00:11:25,920
它
It

783
00:11:25,920 --> 00:11:26,080
是
Is

784
00:11:26,080 --> 00:11:26,760
正常的
Normal

785
00:11:26,760 --> 00:11:27,320
完全
Completely

786
00:11:27,320 --> 00:11:27,640
失去
Lose

787
00:11:27,640 --> 00:11:28,760
均衡
Balance

788
00:11:28,760 --> 00:11:29,000
为什么
Why

789
00:11:29,000 --> 00:11:29,440
DP

790
00:11:29,440 --> 00:11:29,840
研究
Research

791
00:11:29,840 --> 00:11:30,400
正确
Correct

792
00:11:30,400 --> 00:11:30,720
它
It

793
00:11:30,720 --> 00:11:31,040
提供了
Provides

794
00:11:31,040 --> 00:11:31,960
解释
Explanation

795
00:11:31,960 --> 00:11:32,320
是的
Yes

796
00:11:32,320 --> 00:11:32,560
那是
That is

797
00:11:32,560 --> 00:11:33,320
美妙的
Wonderful

798
00:11:33,320 --> 00:11:33,840
分析
Analysis

799
00:11:33,840 --> 00:11:34,280
指出
Points out

800
00:11:34,280 --> 00:11:34,840
代替
Replace

801
00:11:34,840 --> 00:11:35,560
A

802
00:11:35,560 --> 00:11:35,720
与
and

803
00:11:35,720 --> 00:11:36,120
小
small

804
00:11:36,120 --> 00:11:36,880
G

805
00:11:36,880 --> 00:11:37,320
的
of

806
00:11:37,320 --> 00:11:37,680
均衡
balance

807
00:11:37,680 --> 00:11:38,600
与
and

808
00:11:38,600 --> 00:11:38,920
近距离
close-range

809
00:11:38,920 --> 00:11:38,960
的
of

810
00:11:38,960 --> 00:11:39,480
均衡
balance

811
00:11:39,480 --> 00:11:39,680
例如
for example

812
00:11:39,680 --> 00:11:40,360
Y50

813
00:11:40,360 --> 00:11:40,440
和
and

814
00:11:40,440 --> 00:11:41,520
F36

815
00:11:41,520 --> 00:11:41,680
它
it

816
00:11:41,680 --> 00:11:42,240
指出
points out

817
00:11:42,240 --> 00:11:42,840
结构
structure

818
00:11:42,840 --> 00:11:43,280
原因
reason

819
00:11:43,280 --> 00:11:43,680
啊
ah

820
00:11:43,680 --> 00:11:44,120
提供
provide

821
00:11:44,120 --> 00:11:44,440
理解
Understand

822
00:11:44,440 --> 00:11:44,640
不
No

823
00:11:44,640 --> 00:11:44,880
只是
Just

824
00:11:44,880 --> 00:11:45,240
数字
Number

825
00:11:45,240 --> 00:11:45,840
正确
Correct

826
00:11:45,840 --> 00:11:46,200
另一个
Another

827
00:11:46,200 --> 00:11:46,560
例子
Example

828
00:11:46,560 --> 00:11:47,880
106XA

829
00:11:47,880 --> 00:11:48,400
核心
Core

830
00:11:48,400 --> 00:11:49,480
H51A

831
00:11:49,480 --> 00:11:49,800
DP

832
00:11:49,800 --> 00:11:50,120
研究
Research

833
00:11:50,120 --> 00:11:50,560
预测
Predict

834
00:11:50,560 --> 00:11:52,200
0.8kM

835
00:11:52,200 --> 00:11:52,680
实际
Actual

836
00:11:52,680 --> 00:11:52,840
是
Is

837
00:11:52,840 --> 00:11:54,160
0.7kM

838
00:11:54,160 --> 00:11:54,840
再一次
Again

839
00:11:54,840 --> 00:11:56,080
很近
Very close

840
00:11:56,080 --> 00:11:56,360
而
While

841
00:11:56,360 --> 00:11:56,760
前面的
previous

842
00:11:56,760 --> 00:11:57,520
方法
method

843
00:11:57,520 --> 00:11:58,440
前面的方法
previous method

844
00:11:58,440 --> 00:11:59,520
错误地称它
erroneously called it

845
00:11:59,520 --> 00:12:01,040
稳定
stable

846
00:12:01,040 --> 00:12:01,360
DP

847
00:12:01,360 --> 00:12:02,400
研究
study

848
00:12:02,400 --> 00:12:02,840
指出
pointed out

849
00:12:02,840 --> 00:12:03,920
大量
a large amount

850
00:12:03,920 --> 00:12:03,960
的
of

851
00:12:03,960 --> 00:12:04,920
氧化银合
silver oxide compound

852
00:12:04,920 --> 00:12:05,360
因此
therefore

853
00:12:05,360 --> 00:12:05,880
消除
eliminate

854
00:12:05,880 --> 00:12:07,200
酸化银合
acidified silver compound

855
00:12:07,200 --> 00:12:07,360
它
it

856
00:12:07,360 --> 00:12:07,920
看到
see

857
00:12:07,920 --> 00:12:08,440
这些
these

858
00:12:08,440 --> 00:12:09,160
重要的
important

859
00:12:09,160 --> 00:12:09,880
氧化银合
silver oxide compound

860
00:12:09,880 --> 00:12:10,680
互动
interaction

861
00:12:10,680 --> 00:12:10,840
它
It

862
00:12:10,840 --> 00:12:11,160
似乎
seems

863
00:12:11,160 --> 00:12:11,600
更好地
better

864
00:12:11,600 --> 00:12:12,120
捕捉
to capture

865
00:12:12,120 --> 00:12:13,240
它
it

866
00:12:13,240 --> 00:12:13,520
这些
these

867
00:12:13,520 --> 00:12:14,120
特定
specific

868
00:12:14,120 --> 00:12:14,160
的
of

869
00:12:14,160 --> 00:12:14,560
正确
correct

870
00:12:14,560 --> 00:12:14,600
的
of

871
00:12:14,600 --> 00:12:14,960
理解
understanding

872
00:12:14,960 --> 00:12:15,080
是
is

873
00:12:15,080 --> 00:12:15,840
蛋蛋
eggs

874
00:12:45,840 --> 00:12:46,600
所以
so

875
00:12:46,600 --> 00:12:47,560
这些
these

876
00:12:47,560 --> 00:12:48,920
大型计算机
large computers

877
00:12:48,920 --> 00:12:49,720
能够
can

878
00:12:49,720 --> 00:12:50,440
解决
solve

879
00:12:50,440 --> 00:12:51,720
酸化银合
silver oxide mixture

880
00:12:51,720 --> 00:12:52,760
的
of

881
00:12:52,760 --> 00:12:53,520
问题
Problem

882
00:12:53,520 --> 00:12:54,280
很大
Very big

883
00:12:54,280 --> 00:12:54,800
还有
Also

884
00:12:54,800 --> 00:12:56,080
DMS

885
00:12:56,080 --> 00:12:56,600
数据
Data

886
00:12:56,600 --> 00:12:57,360
提供的
Provided

887
00:12:57,360 --> 00:12:58,120
酸化银合
Silver acidification complex

888
00:12:58,120 --> 00:12:58,640
数据
Data

889
00:12:58,640 --> 00:12:59,160
是
Is

890
00:12:59,160 --> 00:12:59,920
预测
Prediction

891
00:12:59,920 --> 00:13:00,680
均等
Equal

892
00:13:00,680 --> 00:13:01,720
变化
Change

893
00:13:01,720 --> 00:13:02,480
最高
Highest

894
00:13:02,480 --> 00:13:03,000
的
Of

895
00:13:03,000 --> 00:13:03,760
数据
Data

896
00:13:03,760 --> 00:13:04,280
预测
Prediction

897
00:13:04,280 --> 00:13:05,040
最大
Maximum

898
00:13:05,040 --> 00:13:05,560
效果
Effect

899
00:13:05,560 --> 00:13:06,320
通常
Usually

900
00:13:06,320 --> 00:13:07,080
是
Is

901
00:13:07,080 --> 00:13:08,120
实际
Actual

902
00:13:08,120 --> 00:13:08,880
高
High

903
00:13:08,880 --> 00:13:09,840
试验
Test

904
00:13:09,840 --> 00:13:10,400
DGA

905
00:13:10,400 --> 00:13:11,000
价值
Value

906
00:13:11,000 --> 00:13:11,960
意味着
Means

907
00:13:11,960 --> 00:13:12,720
它
It

908
00:13:12,720 --> 00:13:13,240
最强的
Strongest

909
00:13:13,240 --> 00:13:13,740
预测
Predict

910
00:13:13,740 --> 00:13:14,520
可能
Possible

911
00:13:14,520 --> 00:13:15,280
会
Will

912
00:13:15,280 --> 00:13:16,040
出现
Occur

913
00:13:16,040 --> 00:13:16,560
正确
Correct

914
00:13:16,560 --> 00:13:17,080
的
Of

915
00:13:17,080 --> 00:13:17,840
稳定
Stable

916
00:13:17,840 --> 00:13:18,600
变化
Change

917
00:13:18,600 --> 00:13:19,380
研究员
Researcher

918
00:13:19,380 --> 00:13:19,880
经常
Often

919
00:13:19,880 --> 00:13:20,400
寻找的
Sought

920
00:13:20,400 --> 00:13:21,160
它
It

921
00:13:21,160 --> 00:13:21,680
像是
like

922
00:13:21,680 --> 00:13:22,440
找到
find

923
00:13:22,440 --> 00:13:23,480
好东西的
good stuff

924
00:13:23,480 --> 00:13:24,240
更好的
better

925
00:13:24,240 --> 00:13:24,760
filter

926
00:13:24,760 --> 00:13:25,520
找到
find

927
00:13:25,520 --> 00:13:26,040
酸化银合
silver oxide compound

928
00:13:26,040 --> 00:13:26,540
更多的
more

929
00:13:26,540 --> 00:13:27,060
效果
effect

930
00:13:27,060 --> 00:13:27,560
OK

931
00:13:27,560 --> 00:13:27,820
所以
so

932
00:13:27,820 --> 00:13:28,340
表现
performance

933
00:13:28,340 --> 00:13:28,840
明显
obvious

934
00:13:28,840 --> 00:13:29,620
高级
advanced

935
00:13:29,620 --> 00:13:30,120
回顾
review

936
00:13:30,120 --> 00:13:30,640
为什么
why

937
00:13:30,640 --> 00:13:31,160
你
you

938
00:13:31,160 --> 00:13:31,660
提到
mention

939
00:13:31,660 --> 00:13:32,180
这两个
these two

940
00:13:32,180 --> 00:13:32,680
重要的
important

941
00:13:32,680 --> 00:13:33,200
策略
Strategy

942
00:13:33,200 --> 00:13:33,720
自动
Automatic

943
00:13:33,720 --> 00:13:34,480
探测
Detect

944
00:13:34,480 --> 00:13:35,000
和
And

945
00:13:35,000 --> 00:13:35,500
相反
Opposite

946
00:13:35,500 --> 00:13:35,760
的
Of

947
00:13:35,760 --> 00:13:36,520
限制
Limit

948
00:13:36,520 --> 00:13:37,300
他们
Them

949
00:13:37,300 --> 00:13:37,800
实际
Actual

950
00:13:37,800 --> 00:13:38,580
看起来
Appear

951
00:13:38,580 --> 00:13:39,080
非常
Very

952
00:13:39,080 --> 00:13:39,600
重要
Important

953
00:13:39,600 --> 00:13:40,120
根据
According to

954
00:13:40,120 --> 00:13:40,620
酸化银合
Silver acidification

955
00:13:40,620 --> 00:13:40,880
研究
Research

956
00:13:40,880 --> 00:13:41,400
他们
They

957
00:13:41,400 --> 00:13:41,640
测试
Test

958
00:13:41,640 --> 00:13:42,160
研究
Study

959
00:13:42,160 --> 00:13:42,680
这些
These

960
00:13:42,680 --> 00:13:42,920
部分
Parts

961
00:13:42,920 --> 00:13:43,440
取消
Cancel

962
00:13:45,280 --> 00:13:46,040
表现
Performance

963
00:13:46,040 --> 00:13:46,820
明显
Obvious

964
00:13:46,820 --> 00:13:47,840
下降
Decline

965
00:13:47,840 --> 00:13:48,600
DG

966
00:13:48,600 --> 00:13:49,120
预测
Predict

967
00:13:49,120 --> 00:13:50,400
SCC

968
00:13:50,400 --> 00:13:50,920
PCC

969
00:13:50,920 --> 00:13:51,420
下降
Decrease

970
00:13:51,420 --> 00:13:52,440
约3%
Approximately 3%

971
00:13:52,440 --> 00:13:53,720
错误
Error

972
00:13:53,720 --> 00:13:54,500
RMSE

973
00:13:54,500 --> 00:13:55,260
MAE

974
00:13:55,260 --> 00:13:56,280
6%

975
00:13:56,280 --> 00:13:57,060
4%

976
00:13:57,060 --> 00:13:57,820
相互
Mutual

977
00:13:57,820 --> 00:13:58,340
所以
Therefore

978
00:13:58,340 --> 00:13:59,360
并不是一个小改变
Not a minor change

979
00:13:59,360 --> 00:13:59,880
实际上
Actually

980
00:13:59,880 --> 00:14:00,380
模组
Module

981
00:14:00,380 --> 00:14:00,900
学得更好
Learn better

982
00:14:00,900 --> 00:14:01,400
绝对
Absolutely

983
00:14:01,400 --> 00:14:01,920
它
It

984
00:14:01,920 --> 00:14:02,440
提升
Improve

985
00:14:02,440 --> 00:14:02,940
总结
Summarize

986
00:14:02,940 --> 00:14:03,720
他们甚至
They even

987
00:14:03,720 --> 00:14:04,220
显示
Show

988
00:14:04,220 --> 00:14:05,240
在测试
In testing

989
00:14:05,240 --> 00:14:05,760
完全
Completely

990
00:14:05,760 --> 00:14:06,280
无线
Wireless

991
00:14:06,280 --> 00:14:06,780
的
Of

992
00:14:06,780 --> 00:14:07,300
设备
Device

993
00:14:07,300 --> 00:14:07,800
进行
Conduct

994
00:14:07,800 --> 00:14:08,320
不同的
Different

995
00:14:08,320 --> 00:14:08,840
技术
Technology

996
00:14:08,840 --> 00:14:09,340
增加
Increase

997
00:14:09,340 --> 00:14:09,860
表现
Performance

998
00:14:09,860 --> 00:14:10,360
更多
More

999
00:14:10,360 --> 00:14:10,880
这也使
This also makes

1000
00:14:10,880 --> 00:14:11,400
模组
Module

1001
00:14:11,400 --> 00:14:11,900
更坚硬
Harder

1002
00:14:11,900 --> 00:14:12,420
OK

1003
00:14:12,420 --> 00:14:12,920
和
And

1004
00:14:12,920 --> 00:14:13,440
相反
Opposite

1005
00:14:13,440 --> 00:14:13,960
限制
Limit

1006
00:14:13,960 --> 00:14:15,240
再提醒
Remind again

1007
00:14:15,240 --> 00:14:16,000
A to B

1008
00:14:16,000 --> 00:14:16,780
改变
Change

1009
00:14:16,780 --> 00:14:18,060
是BDA的相反
Is the opposite of BDA

1010
00:14:18,060 --> 00:14:18,560
对
Correct

1011
00:14:18,560 --> 00:14:18,820
那
That

1012
00:14:18,820 --> 00:14:19,340
基本的
Basic

1013
00:14:19,340 --> 00:14:19,840
适当
Appropriate

1014
00:14:19,840 --> 00:14:20,880
适当
Appropriate

1015
00:14:20,880 --> 00:14:22,160
老人
Elderly

1016
00:14:22,160 --> 00:14:22,920
模组
Mod

1017
00:14:22,920 --> 00:14:23,680
有多坚硬
How hard

1018
00:14:23,680 --> 00:14:24,200
挺坚硬
Quite hard

1019
00:14:24,200 --> 00:14:24,960
有时候
Sometimes

1020
00:14:24,960 --> 00:14:26,500
A B B A的预测
A B B A prediction

1021
00:14:26,500 --> 00:14:27,020
可能是
May be

1022
00:14:27,020 --> 00:14:27,780
不一样的
Different

1023
00:14:27,780 --> 00:14:28,560
不坚硬
Not hard

1024
00:14:28,560 --> 00:14:30,080
如果你依靠
If you rely on

1025
00:14:30,080 --> 00:14:30,600
预测
Prediction

1026
00:14:30,600 --> 00:14:31,120
这些
These

1027
00:14:31,120 --> 00:14:31,620
预测
Predictions

1028
00:14:31,620 --> 00:14:31,880
增加
Increase

1029
00:14:31,880 --> 00:14:32,640
限制
Limit

1030
00:14:32,900 --> 00:14:33,680
DPSTAB

1031
00:14:33,680 --> 00:14:34,440
修改
Modify

1032
00:14:34,440 --> 00:14:35,200
大幅
Significantly

1033
00:14:35,720 --> 00:14:36,740
使模组
Make the module

1034
00:14:36,740 --> 00:14:37,520
遵循这个
Follow this

1035
00:14:37,520 --> 00:14:38,020
规则
Rule

1036
00:14:38,020 --> 00:14:38,800
DPSTAB

1037
00:14:38,800 --> 00:14:39,560
使预测
Make predictions

1038
00:14:39,560 --> 00:14:40,320
这些
These

1039
00:14:40,320 --> 00:14:40,840
向前
Forward

1040
00:14:40,840 --> 00:14:41,360
反转
Reverse

1041
00:14:41,360 --> 00:14:41,860
变化
change

1042
00:14:41,860 --> 00:14:42,380
变得
become

1043
00:14:42,380 --> 00:14:42,880
更小
smaller

1044
00:14:43,140 --> 00:14:43,920
出现
appear

1045
00:14:43,920 --> 00:14:44,680
几乎
almost

1046
00:14:44,680 --> 00:14:45,200
完全
completely

1047
00:14:45,200 --> 00:14:45,720
相反
opposite

1048
00:14:45,720 --> 00:14:46,480
应该是
should be

1049
00:14:46,740 --> 00:14:48,020
这就让整个系统
This makes the entire system

1050
00:14:48,020 --> 00:14:48,780
更有信心
more confident

1051
00:14:48,780 --> 00:14:49,300
实用
practical

1052
00:14:49,300 --> 00:14:50,320
实用
practical

1053
00:14:50,320 --> 00:14:50,840
正确
correct

1054
00:14:50,840 --> 00:14:51,860
你可以依靠
you can rely on

1055
00:14:51,860 --> 00:14:52,360
预测
predict

1056
00:14:52,360 --> 00:14:52,880
相反的
opposite

1057
00:14:52,880 --> 00:14:53,400
关系
relationship

1058
00:14:53,400 --> 00:14:53,900
持续
continue

1059
00:14:53,900 --> 00:14:54,680
预测
predict

1060
00:14:54,680 --> 00:14:55,440
增加
increase

1061
00:14:55,440 --> 00:14:55,960
坚硬
Hard

1062
00:14:55,960 --> 00:14:56,460
预测
Predict

1063
00:14:56,720 --> 00:14:57,240
所以
So

1064
00:14:57,240 --> 00:14:58,260
我们把这些都拉起来
We pull all these up

1065
00:14:58,260 --> 00:14:58,760
这是什么
What is this

1066
00:14:58,760 --> 00:14:59,280
大图
Big picture

1067
00:14:59,280 --> 00:14:59,800
这是什么
What is this

1068
00:14:59,800 --> 00:15:00,560
DPSTAB的主要
The main

1069
00:15:00,560 --> 00:15:01,080
优势
Advantage

1070
00:15:01,080 --> 00:15:01,840
在什么
On what

1071
00:15:01,840 --> 00:15:02,600
标题上
Title

1072
00:15:02,860 --> 00:15:03,640
首先
First

1073
00:15:03,640 --> 00:15:04,140
它是
It is

1074
00:15:04,140 --> 00:15:04,920
连续
Continuous

1075
00:15:04,920 --> 00:15:05,420
那是
That is

1076
00:15:05,420 --> 00:15:05,940
巨大
Huge

1077
00:15:05,940 --> 00:15:06,440
不需要
Not needed

1078
00:15:06,440 --> 00:15:06,960
那些
Those

1079
00:15:06,960 --> 00:15:07,480
难得
Rare

1080
00:15:07,480 --> 00:15:07,980
得到3D的
Get 3D

1081
00:15:07,980 --> 00:15:08,500
结构
structure

1082
00:15:08,500 --> 00:15:09,000
使它
make it

1083
00:15:09,000 --> 00:15:09,780
非常适合
highly suitable

1084
00:15:09,780 --> 00:15:10,280
是的
yes

1085
00:15:10,540 --> 00:15:11,060
第二
second

1086
00:15:11,060 --> 00:15:11,820
它能够
it can

1087
00:15:11,820 --> 00:15:12,600
正确地
correctly

1088
00:15:12,600 --> 00:15:13,100
预测
predict

1089
00:15:13,100 --> 00:15:14,640
坚硬的变化
rigid changes

1090
00:15:14,640 --> 00:15:15,400
根据
according to

1091
00:15:15,400 --> 00:15:16,420
PLLM

1092
00:15:16,420 --> 00:15:17,200
来说
in terms of

1093
00:15:17,200 --> 00:15:17,960
它能够看到
it can observe

1094
00:15:17,960 --> 00:15:18,980
巨大的变化
significant changes

1095
00:15:18,980 --> 00:15:19,500
根据
according to

1096
00:15:19,500 --> 00:15:20,020
PLLM

1097
00:15:20,020 --> 00:15:20,780
它能够看到
it can observe

1098
00:15:20,780 --> 00:15:21,540
巨大的变化
significant changes

1099
00:15:21,540 --> 00:15:22,060
根据
according to

1100
00:15:22,060 --> 00:15:22,820
巨大的变化
significant changes

1101
00:15:22,820 --> 00:15:23,340
对
Correct

1102
00:15:23,340 --> 00:15:23,860
第三
Third

1103
00:15:23,860 --> 00:15:24,360
它能够
It can

1104
00:15:24,360 --> 00:15:24,880
评估
Evaluate

1105
00:15:24,880 --> 00:15:25,380
坚硬的
Hard

1106
00:15:25,380 --> 00:15:25,900
坚硬的
Hard

1107
00:15:25,900 --> 00:15:26,420
坚硬的
Hard

1108
00:15:26,420 --> 00:15:26,920
变化
Change

1109
00:15:27,180 --> 00:15:27,700
例如
For example

1110
00:15:27,700 --> 00:15:28,200
维生素
Vitamin

1111
00:15:28,200 --> 00:15:28,980
维生素
Vitamin

1112
00:15:29,220 --> 00:15:29,740
能够
Able to

1113
00:15:29,740 --> 00:15:30,500
提供
Provide

1114
00:15:30,500 --> 00:15:31,020
有效的
Effective

1115
00:15:31,020 --> 00:15:31,540
变化
Change

1116
00:15:31,540 --> 00:15:32,040
所以
So

1117
00:15:32,040 --> 00:15:32,820
苹果工程师
Apple engineer

1118
00:15:32,820 --> 00:15:33,320
或
Or

1119
00:15:33,320 --> 00:15:34,100
生物工程师
Bioengineer

1120
00:15:34,100 --> 00:15:34,860
它提供
It provides

1121
00:15:34,860 --> 00:15:35,380
快速
Fast

1122
00:15:35,380 --> 00:15:35,880
实用
Practical

1123
00:15:35,880 --> 00:15:36,400
和
And

1124
00:15:36,400 --> 00:15:36,900
高级的
Advanced

1125
00:15:36,900 --> 00:15:37,420
工程
Engineering

1126
00:15:37,680 --> 00:15:38,180
它能够
It can

1127
00:15:38,180 --> 00:15:38,960
迅速
Quickly

1128
00:15:38,960 --> 00:15:39,960
设计更好的
Design better

1129
00:15:39,960 --> 00:15:40,500
维生素
Vitamins

1130
00:15:40,500 --> 00:15:41,000
无论是
Whether

1131
00:15:41,000 --> 00:15:41,520
治疗
Treatment

1132
00:15:41,520 --> 00:15:42,280
诊断
Diagnosis

1133
00:15:42,280 --> 00:15:42,800
或
Or

1134
00:15:42,800 --> 00:15:43,560
工程
Engineering

1135
00:15:44,340 --> 00:15:45,100
减少猜测
Reduce guesswork

1136
00:15:45,360 --> 00:15:45,880
更快的
Faster

1137
00:15:45,880 --> 00:15:46,380
发现
Discovery

1138
00:15:46,380 --> 00:15:46,900
这似乎是一个
This seems like a

1139
00:15:46,900 --> 00:15:47,400
重要的
Significant

1140
00:15:47,400 --> 00:15:47,920
进步
Advancement

1141
00:15:47,920 --> 00:15:48,180
是的
Yes

1142
00:15:48,440 --> 00:15:49,200
接下来
Next

1143
00:15:49,200 --> 00:15:49,720
这些
These

1144
00:15:49,720 --> 00:15:50,480
未来的挑战
Future challenges

1145
00:15:50,480 --> 00:15:51,500
或者未来的方向
Or future directions

1146
00:15:51,500 --> 00:15:52,020
在这些
In these

1147
00:15:52,280 --> 00:15:52,780
领域
Fields

1148
00:15:52,780 --> 00:15:53,300
肯定有
There must be

1149
00:15:53,300 --> 00:15:53,800
未来的挑战
Future challenges

1150
00:15:53,800 --> 00:15:54,320
一个
One

1151
00:15:54,320 --> 00:15:54,840
是
Is

1152
00:15:54,840 --> 00:15:55,860
更细腻的
More refined

1153
00:15:55,860 --> 00:15:56,360
细节
Details

1154
00:15:56,360 --> 00:15:56,880
例如
For example

1155
00:15:56,880 --> 00:15:57,400
细节
Details

1156
00:15:57,400 --> 00:15:57,900
细节
Details

1157
00:15:57,900 --> 00:15:58,420
细节
Details

1158
00:15:58,420 --> 00:15:58,920
细节
Details

1159
00:15:58,920 --> 00:15:59,440
细节
Details

1160
00:15:59,440 --> 00:15:59,960
细节
Details

1161
00:15:59,960 --> 00:16:00,460
细节
Detail

1162
00:16:00,460 --> 00:16:00,980
细节
Detail

1163
00:16:00,980 --> 00:16:01,480
细节
Detail

1164
00:16:01,480 --> 00:16:02,000
细节
Detail

1165
00:16:02,000 --> 00:16:02,520
细节
Detail

1166
00:16:02,520 --> 00:16:03,020
细节
Detail

1167
00:16:03,020 --> 00:16:03,540
细节
Detail

1168
00:16:03,540 --> 00:16:04,040
细节
Detail

1169
00:16:04,040 --> 00:16:05,060
细节
Detail

1170
00:16:05,060 --> 00:16:05,580
细节
Detail

1171
00:16:09,680 --> 00:16:10,700
细节
Detail

1172
00:16:10,700 --> 00:16:11,200
细节
Detail

1173
00:16:11,460 --> 00:16:12,240
细节
Detail

1174
00:16:12,480 --> 00:16:13,260
细节
Detail

1175
00:16:15,820 --> 00:16:16,320
细节
Detail

1176
00:16:16,660 --> 00:16:17,160
细节
Detail

1177
00:16:23,500 --> 00:16:24,000
细节
Detail

1178
00:16:26,820 --> 00:16:27,600
细节
Detail

1179
00:16:31,440 --> 00:16:32,200
细节
Detail

1180
00:16:32,720 --> 00:16:33,220
细节
Detail

1181
00:16:33,480 --> 00:16:34,000
细节
Details

1182
00:16:34,000 --> 00:16:34,020
细节
Details

1183
00:16:34,020 --> 00:16:36,020
是一个很好的例子
is a great example

1184
00:16:36,020 --> 00:16:38,020
如何有精致的智能智能
How to have exquisite intelligence

1185
00:16:38,020 --> 00:16:40,020
像这些大语言模式
Like these large language models

1186
00:16:40,020 --> 00:16:42,020
可以去解决
can solve

1187
00:16:42,020 --> 00:16:44,020
这些有关生物的困难
these biology-related difficulties

1188
00:16:44,020 --> 00:16:46,020
绝对是这样的
Absolutely

1189
00:16:46,020 --> 00:16:48,020
它显示了这些系统的秘密
It reveals the secrets of these systems

1190
00:16:48,020 --> 00:16:50,020
当你想到这些可能性
When you think about these possibilities

1191
00:16:50,020 --> 00:16:52,020
能够精准地
to precisely

1192
00:16:52,020 --> 00:16:54,020
制造特定工作的生物
create organisms for specific tasks

1193
00:16:54,020 --> 00:16:56,020
我意思是
I mean

1194
00:16:56,020 --> 00:16:58,020
设计药品
designing drugs

1195
00:16:58,020 --> 00:17:00,020
比较稳定
relatively stable

1196
00:17:00,020 --> 00:17:02,020
长期在身体里
long-lasting in the body

1197
00:17:02,020 --> 00:17:04,020
专注于真正能够
focusing on truly being able to

1198
00:17:04,020 --> 00:17:06,020
在最基础的程度上设计生物
design organisms at the most fundamental level

1199
00:17:06,020 --> 00:17:08,020
这真是开启了人的想象
This really sparks the imagination

1200
00:17:08,020 --> 00:17:10,020
什么其他超级复杂的
What other super-complex

1201
00:17:10,020 --> 00:17:12,020
生物图案
Biological patterns

1202
00:17:12,020 --> 00:17:14,020
除了基础稳定
Beyond basic stability

1203
00:17:14,020 --> 00:17:16,020
我们还能怎么解决
How else can we solve it

1204
00:17:16,020 --> 00:17:18,020
使用这些深入学习的工具
Using these deep learning tools

1205
00:17:18,020 --> 00:17:20,020
什么是最后的大线链
What is the final major chain

1206
00:17:20,020 --> 00:17:22,020
那是个兴奋的问题
That's an exciting question

1207
00:17:22,020 --> 00:17:24,020
可能性似乎很大
The possibilities seem vast
