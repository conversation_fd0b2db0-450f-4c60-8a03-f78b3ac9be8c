1
00:00:00,000 --> 00:00:00,880
欢迎收听
ようこそ

2
00:00:01,280 --> 00:00:05,640
今天我们来深入聊聊一个生物计算理喻的新东西
今日は生物計算理論の新しいトピックについて深く話し合います

3
00:00:05,880 --> 00:00:08,200
关于蛋白质稳定性的预测
タンパク質の安定性予測について

4
00:00:08,440 --> 00:00:10,760
具体来说就是蛋白质里头
具体的にはタンパク質内部で

5
00:00:11,000 --> 00:00:12,800
一个安计算变了
一つのアミノ酸が変化し

6
00:00:13,040 --> 00:00:13,840
发生突变了
突然変異が起こると

7
00:00:14,080 --> 00:00:15,600
它的稳定性会怎么变
その安定性はどう変化するか

8
00:00:16,120 --> 00:00:17,400
我们参考了一篇乱文
ある論文を参考にしました

9
00:00:17,680 --> 00:00:18,960
介绍了一种叫
DP stepと呼ばれる新しい手法を

10
00:00:19,200 --> 00:00:21,760
DP step的新方法
紹介しています

11
00:00:22,000 --> 00:00:24,320
这次我们就想跟你一起弄明白
今回は皆さんと一緒に理解したいと思います

12
00:00:24,320 --> 00:00:26,360
这个DP step到底是什么
このDP stepとは何か

13
00:00:26,360 --> 00:00:27,640
它为啥重要
なぜ重要なのか

14
00:00:27,640 --> 00:00:29,960
跟现在的方法比它好在哪儿
既存の手法と比べてどこが優れているか

15
00:00:30,320 --> 00:00:31,280
你可能会问
こう思うかもしれません

16
00:00:31,520 --> 00:00:34,080
预测稳定性它有啥用呢
安定性予測にどんな意味があるのかと

17
00:00:34,600 --> 00:00:38,720
其实这在蛋白质工程你想想改造个没什么的
実はタンパク質工学において改変する際や

18
00:00:38,720 --> 00:00:41,520
还有药收集里都特别关键
創薬研究においても極めて重要です

19
00:00:42,280 --> 00:00:46,400
我们都知道预测蛋白质稳定性一直挺难的
タンパク質安定性の予測は常に難しい課題でした

20
00:00:46,880 --> 00:00:47,920
以前的方法
従来の方法では

21
00:00:48,160 --> 00:00:49,960
要么就只看序列
シーケンスだけを見るか

22
00:00:50,240 --> 00:00:52,520
但蛋白质它是个三维的东西对吧
でもタンパク質は三次元のものですよね

23
00:00:52,800 --> 00:00:53,280
对
はい

24
00:00:53,280 --> 00:00:57,640
观看序列很难抓住结构变化带来了影响
シーケンスを見るだけでは構造変化の影響を捉えにくい

25
00:00:58,120 --> 00:01:01,600
要么就得有那个精确的蛋白质三维结构
正確なタンパク質の三次元構造が必要か

26
00:01:01,600 --> 00:01:02,200
是
はい

27
00:01:02,480 --> 00:01:05,440
但高质量的结构数据获取起来太难了
でも高品質な構造データの取得は非常に難しい

28
00:01:05,440 --> 00:01:06,280
成问又高
コストも高い

29
00:01:06,280 --> 00:01:06,800
没错
その通り

30
00:01:07,200 --> 00:01:08,280
而且还有一个问题
そしてもう一つ問題があります

31
00:01:08,280 --> 00:01:10,080
就是数据不平衡
データの不均衡です

32
00:01:10,400 --> 00:01:12,680
这个确实是个大问题
これは確かに大きな問題です

33
00:01:12,680 --> 00:01:13,640
就是说
つまり

34
00:01:13,920 --> 00:01:17,240
已知的能让蛋白质更稳定的突变例子
タンパク質をより安定させる変異の既知例は

35
00:01:17,560 --> 00:01:20,640
比那种破坏稳定性的要少得多得多
安定性を破壊するタイプよりも圧倒的に少ない

36
00:01:20,640 --> 00:01:21,120
对
はい

37
00:01:21,120 --> 00:01:24,040
这就导致模型训练的时候容易有偏见
これによりモデル訓練時にバイアスが生じやすい

38
00:01:24,040 --> 00:01:26,800
所以这就影响预测的准头了
だから予測の精度に影響するんです

39
00:01:26,800 --> 00:01:27,440
是的
そうです

40
00:01:27,960 --> 00:01:28,440
不过呢
ただし

41
00:01:28,440 --> 00:01:29,600
这个DpStab
このDpStab

42
00:01:29,600 --> 00:01:31,960
它就是冲着解决这些痛点来的
これらの痛点を解決するために来た

43
00:01:32,360 --> 00:01:34,760
它是一个基于深部学习的方法
深層学習に基づく方法です

44
00:01:35,040 --> 00:01:36,520
最妙的地方在于
最も素晴らしいところは

45
00:01:36,640 --> 00:01:37,320
啊
ああ

46
00:01:37,320 --> 00:01:41,000
它主要是靠蛋白质的安基酸序列信息
主にタンパク質のアミノ酸配列情報に依存している

47
00:01:41,000 --> 00:01:41,560
哦
おお

48
00:01:41,560 --> 00:01:42,720
主要靠序列
主に配列に依存

49
00:01:42,720 --> 00:01:43,240
对
そう

50
00:01:43,520 --> 00:01:48,360
它就比较巧妙地绕开了对高质量三维结构数据的那个硬性要求
高品質な3D構造データの厳しい要求を巧みに回避している

51
00:01:48,880 --> 00:01:52,000
它用了现在挺火的那个蛋白质大语言模型
今流行りのタンパク質大規模言語モデルを使っている

52
00:01:52,240 --> 00:01:54,160
具体来说是ESM2
具体的にはESM2

53
00:01:54,400 --> 00:01:55,240
来打击处
処理するために

54
00:01:56,240 --> 00:01:59,720
然后呢通过一个叫交叉注意力的机制
そして、クロスアテンションと呼ばれるメカニズムを通じて

55
00:01:59,720 --> 00:02:04,520
特别去关注突变点周围那些安基酸的接触方式是怎么变的
特に変異点周辺のアミノ酸の接触方式の変化に注目する

56
00:02:04,520 --> 00:02:05,360
等一下
ちょっと待って

57
00:02:05,360 --> 00:02:09,320
交叉注意力这个听起来有点抽象
クロスアテンションって少し抽象的だね

58
00:02:09,320 --> 00:02:10,720
它大概是怎么回事
どういう仕組みなんだろう

59
00:02:10,720 --> 00:02:11,320
嗯
うん

60
00:02:11,320 --> 00:02:12,360
你可以这么想
こう考えてもいいよ

61
00:02:12,360 --> 00:02:14,240
就是我们理解一个词
つまり、ある単語を理解するには

62
00:02:14,240 --> 00:02:16,480
不光要看这个词本身
その単語自体だけでなく

63
00:02:16,480 --> 00:02:18,600
还得看它旁边挨着的词
隣接する単語も見る必要がある

64
00:02:18,600 --> 00:02:21,560
限制整个句子的意思对吧
文全体の意味を制約するのですね

65
00:02:21,560 --> 00:02:23,120
这个比喻田形象的
この比喩はとても形象的だ

66
00:02:23,120 --> 00:02:23,720
对
はい

67
00:02:24,040 --> 00:02:25,960
这个交叉注意力机制
このクロスアテンション機構は

68
00:02:25,960 --> 00:02:29,160
就是让模型在分析那个突变位点的时候
モデルが変異部位を分析する際に

69
00:02:29,160 --> 00:02:34,120
能自动的动态的去关注那些跟它有消货作用的
自動的に動的に相互作用を持つ

70
00:02:34,120 --> 00:02:37,000
或者说离得比较近的关键安基酸
または近くにある重要なアミノ酸に注目できるようにする

71
00:02:37,360 --> 00:02:40,080
它会评估这些旁边的残疾
これらの周辺の残基を評価し

72
00:02:40,080 --> 00:02:42,240
因为这个突变发生了什么变化
変異によって何が変化したかを

73
00:02:42,240 --> 00:02:44,520
然后把这些信息综合起来
そしてこれらの情報を総合して

74
00:02:44,520 --> 00:02:46,960
判断对整体稳定性的影响
全体の安定性への影響を判断する

75
00:02:46,960 --> 00:02:47,800
哦
ああ

76
00:02:47,800 --> 00:02:48,480
明白了
分かりました

77
00:02:48,480 --> 00:02:53,240
就是说更关注突变点附近的小环境变化
つまり変異点周辺の微小環境変化に注目する

78
00:02:53,240 --> 00:02:54,760
可以这么理解
そう理解しても良い

79
00:02:54,760 --> 00:02:57,680
然后它还有两个很关键的策略
そして、2つの重要な戦略がある

80
00:02:57,680 --> 00:02:58,200
嗯
うん

81
00:02:58,200 --> 00:02:59,600
一个叫自我征流
自己徴流というもの

82
00:02:59,600 --> 00:03:00,440
自我征流
自己徴流

83
00:03:00,440 --> 00:03:03,320
自己给自己征流点什么出来
自分で自分に何かを徴流する

84
00:03:03,320 --> 00:03:04,040
哈哈
ははは

85
00:03:04,040 --> 00:03:05,520
有点那个意思
そんな感じ

86
00:03:05,520 --> 00:03:06,720
你可以想象成
こう想像してみて

87
00:03:06,720 --> 00:03:08,480
模型在训练的时候啊
モデルが訓練する時に

88
00:03:08,480 --> 00:03:10,920
把早期训练的还不错的自己
初期段階でうまく訓練できた自分を

89
00:03:10,920 --> 00:03:12,120
当做一个老师
教師として使う

90
00:03:12,120 --> 00:03:12,520
嗯
うん

91
00:03:12,520 --> 00:03:13,120
老师
教師

92
00:03:13,120 --> 00:03:13,600
对
そう

93
00:03:13,600 --> 00:03:17,320
用这个老师模型去生成一些额外的训练数据
この教師モデルで追加の訓練データを生成する

94
00:03:17,320 --> 00:03:19,440
就是所谓的尾标签
いわゆる擬似ラベル

95
00:03:19,440 --> 00:03:22,920
然后用这些数据再去知道后面阶段的训练
そのデータを使って後段の訓練を指導する

96
00:03:23,000 --> 00:03:24,000
哦
ああ

97
00:03:24,000 --> 00:03:25,440
这做的好处是
これの利点は

98
00:03:25,440 --> 00:03:28,720
好处就是能增加训练数据的多样性嘛
訓練データの多様性が増せること

99
00:03:28,720 --> 00:03:31,240
让最终的模型学得更全面
最終モデルの学習をより包括的にする

100
00:03:31,240 --> 00:03:34,480
不容易只在某些特定数据上表现好
特定データだけに偏らずに済む

101
00:03:34,480 --> 00:03:37,240
说白了就是提高它的泛化能力
要するに、その汎化能力を高めることです

102
00:03:37,240 --> 00:03:39,360
让模型更见多视广
モデルにより広い視野を持たせる

103
00:03:39,360 --> 00:03:40,760
那另一个策略呢
では、もう一つの戦略は？

104
00:03:40,760 --> 00:03:42,960
另一个是反对称约数
もう一つは反対称除数です

105
00:03:42,960 --> 00:03:44,720
这个呃
この、えーと

106
00:03:44,720 --> 00:03:45,960
就更有意思了
さらに面白いです

107
00:03:45,960 --> 00:03:48,960
因为它利用了基本的物理化学原理
基本的な物理化学原理を利用しているからです

108
00:03:48,960 --> 00:03:49,960
物理化学原理

109
00:03:49,960 --> 00:03:50,840
是的
はい

110
00:03:50,840 --> 00:03:51,720
你想啊
考えてみてください

111
00:03:51,720 --> 00:03:53,320
比如一个按计算A
例えば、計算Aに従って

112
00:03:53,320 --> 00:03:54,200
变成B
Bに変化する

113
00:03:54,200 --> 00:03:55,120
稳定性变化
安定性の変化

114
00:03:55,120 --> 00:03:57,040
我们叫它delta delta g
それをデルタデルタGと呼びます

115
00:03:57,040 --> 00:03:57,440
嗯
うん

116
00:03:57,440 --> 00:03:58,280
那反过来
逆に

117
00:03:58,280 --> 00:04:00,040
如果B变回A
もしBがAに戻るなら

118
00:04:00,040 --> 00:04:02,840
理论上稳定性变化就应该是
理論的には安定性の変化は

119
00:04:02,840 --> 00:04:03,240
呃
えーと

120
00:04:03,240 --> 00:04:04,760
-的delta delta g
マイナスのデルタデルタGになるはずです

121
00:04:04,760 --> 00:04:05,360
对吧
そうだよね

122
00:04:05,360 --> 00:04:06,040
呃对
あ、そう

123
00:04:06,040 --> 00:04:07,000
风强相反
風が強いと逆

124
00:04:07,000 --> 00:04:07,680
数值一样
数値は同じ

125
00:04:07,680 --> 00:04:08,520
没错
その通り

126
00:04:08,520 --> 00:04:10,080
这个反对称约数
この反対称の制約

127
00:04:10,080 --> 00:04:12,840
就是强制模型去学习这种对称性
つまりモデルにこの対称性を強制的に学習させる

128
00:04:12,840 --> 00:04:14,600
或者说反对称性
あるいは反対称性を

129
00:04:14,600 --> 00:04:16,400
这个设计非常巧妙
この設計は非常に巧妙だ

130
00:04:16,400 --> 00:04:19,080
因为它不完全是靠数据去擬合
なぜなら完全にデータに依存してフィットするわけではないから

131
00:04:19,080 --> 00:04:20,560
而是把物理规律
物理法則を

132
00:04:20,560 --> 00:04:23,240
可以说是硬塞给模型去遵守
言わばモデルに無理やり守らせている

133
00:04:23,240 --> 00:04:23,840
哇
わあ

134
00:04:23,840 --> 00:04:26,800
这等于是给了模型一个常识性的指导
これはモデルに常識的な指導を与えるようなものだ

135
00:04:26,800 --> 00:04:27,440
对
そう

136
00:04:27,440 --> 00:04:29,560
特别是数据不太够的时候
特にデータが不足している時

137
00:04:29,560 --> 00:04:31,360
这种基于原理的约数
この原理に基づく制約

138
00:04:31,360 --> 00:04:34,000
能大大提高预测结果的可靠性
予測結果の信頼性を大幅に向上させられる

139
00:04:34,000 --> 00:04:36,040
让它更符合真实世界
現実世界により合致させる

140
00:04:36,040 --> 00:04:37,880
听起来理论很强啊
理論的にはかなり強そうだね

141
00:04:37,880 --> 00:04:40,120
那实际跑起来效果怎么样
実際に実行してみた効果はどうですか

142
00:04:40,120 --> 00:04:44,000
它真的能比那些用结构信息的方法还好吗
構造情報を使った方法よりも本当に優れているのでしょうか

143
00:04:44,000 --> 00:04:44,480
诶
えっ

144
00:04:44,480 --> 00:04:46,480
这正是DP step的亮点
これはまさにDP stepの優れた点です

145
00:04:46,480 --> 00:04:48,200
根据论文里给的数据
論文に記載されているデータによると

146
00:04:48,200 --> 00:04:50,520
它在那个独立测试级上的表现
あの独立テストセットでのパフォーマンスは

147
00:04:50,520 --> 00:04:51,840
确实非常亮眼
確かに非常に目覚ましいです

148
00:04:51,840 --> 00:04:52,320
哦
おお

149
00:04:52,320 --> 00:04:53,160
具体说说
具体的に説明してください

150
00:04:53,160 --> 00:04:54,080
你看啊
ほら

151
00:04:54,080 --> 00:04:57,480
衡量预测准确度的几个关键指标
予測精度を測るいくつかの重要な指標

152
00:04:57,480 --> 00:05:01,600
比如预测值和真实稳定性低链花值的那个相关性
例えば予測値と真の安定性低鎖値の相関性

153
00:05:01,600 --> 00:05:04,320
像PCC、SCC这些
PCCやSCCなど

154
00:05:04,320 --> 00:05:07,800
还有区分稳定不稳定图变的准确率
安定と不安定な変異を区別する精度

155
00:05:07,800 --> 00:05:09,880
ACC、MCC

156
00:05:09,880 --> 00:05:13,960
DP step都明显比好几种现有方法要强
DP stepは明らかに既存のいくつかの方法より優れています

157
00:05:13,960 --> 00:05:16,160
包括那些基于结构的方法
構造ベースの方法も含めて

158
00:05:16,160 --> 00:05:16,560
对
はい

159
00:05:16,560 --> 00:05:20,120
甚至包括一些需要精确结构信息的方法
正確な構造情報を必要とする方法さえも

160
00:05:20,120 --> 00:05:21,920
而且它的预测误差
さらにその予測誤差は

161
00:05:21,920 --> 00:05:24,920
就是那个RMSE和MAE
それはRMSEとMAEのことです

162
00:05:24,920 --> 00:05:25,800
也更小
さらに小さい

163
00:05:25,800 --> 00:05:26,680
那很厉害了
それはすごいですね

164
00:05:26,680 --> 00:05:27,520
嗯
うん

165
00:05:27,520 --> 00:05:28,960
特别要提一下的是
特に注目すべきは

166
00:05:28,960 --> 00:05:31,440
它在对大量突变进行排序
大量の変異をランク付けする際に

167
00:05:31,440 --> 00:05:34,440
就是判断哪个突变影响更大这方面
どの変異がより大きな影響を与えるかを判断する点で

168
00:05:34,440 --> 00:05:35,880
表现特别突出
特に優れた性能を発揮します

169
00:05:35,880 --> 00:05:36,680
排序
ランキング

170
00:05:36,680 --> 00:05:37,640
这对什么有用
これは何に役立ちますか

171
00:05:37,640 --> 00:05:39,520
这一对高通量虚险实验
ハイスループットスクリーニング実験において

172
00:05:39,520 --> 00:05:43,320
比如那个DMS数据验证的就非常有价值了
例えばDMSデータによる検証は非常に価値があります

173
00:05:43,320 --> 00:05:46,080
你可以快速摔掉一大批不靠谱的突变
信頼性の低い変異を素早く排除できます

174
00:05:46,160 --> 00:05:47,040
明白了
わかりました

175
00:05:47,040 --> 00:05:49,000
那有没有具体的例子
具体的な例はありますか

176
00:05:49,000 --> 00:05:52,400
能让我们更直观的看到它为啥预测的准
なぜ予測が正確なのかを直感的に理解できるような

177
00:05:52,400 --> 00:05:54,480
路文里分析了几个案例
論文ではいくつかのケースを分析しています

178
00:05:54,480 --> 00:05:58,720
比如说在一个叫RPTLA的蛋白质疑
例えばRPTLAというタンパク質において

179
00:05:58,720 --> 00:06:00,280
一个秉安酸A
アミノ酸Aから

180
00:06:00,280 --> 00:06:02,280
变成肝安酸G的突变
アミノ酸Gへの変異

181
00:06:02,280 --> 00:06:04,400
就是A34G
それはA34Gです

182
00:06:04,400 --> 00:06:07,360
实验证明这个突变会降低稳定性
実験により、この変異は安定性を低下させることが証明されました

183
00:06:07,360 --> 00:06:09,840
DP step就准确预测了这一点
DP stepはこれを正確に予測しました

184
00:06:09,840 --> 00:06:10,840
它是怎么看出来的
どうやって見抜いたのですか

185
00:06:10,840 --> 00:06:12,240
因为它能捕捉到
それを捉えることができるからです

186
00:06:12,240 --> 00:06:15,720
这个突变导致胺基酸测链变小了
この変異によりアミノ酸側鎖が小さくなりました

187
00:06:15,720 --> 00:06:18,480
结果就失去了原本和周围残疾的一些
結果として、周囲との相互作用の一部を失いました

188
00:06:18,480 --> 00:06:19,800
输水相互作用
疎水性相互作用

189
00:06:19,800 --> 00:06:22,080
造成了局部的不稳定
局所的な不安定化を引き起こしました

190
00:06:22,080 --> 00:06:25,960
但另一个基于结构的方法叫GODDG3D
しかし、GODDG3Dという別の構造ベースの手法では

191
00:06:25,960 --> 00:06:27,960
反而觉得这个突变影响不大
この変異の影響は小さいと考えられています

192
00:06:27,960 --> 00:06:28,600
有意思
興味深いですね

193
00:06:28,600 --> 00:06:29,520
还有别的例子吗
他にも例はありますか

194
00:06:29,520 --> 00:06:34,800
还有一个是在EO6XA蛋白里的H51A突变
もう一つはEO6XAタンパク質のH51A変異です

195
00:06:34,800 --> 00:06:38,000
DP step也成功预测了稳定性下降
DP stepは安定性低下の予測にも成功しました

196
00:06:38,000 --> 00:06:41,560
因为它识别出这个突变破坏要原有的轻贱网络
この変異が既存の水素結合ネットワークを破壊すると認識したからです

197
00:06:41,560 --> 00:06:43,960
所以它能抓住这些很细微的变化
だから、このような微妙な変化も捉えられるのです

198
00:06:43,960 --> 00:06:44,600
对
はい

199
00:06:44,640 --> 00:06:48,600
这说明DP step对突变影响的理解可能更深入
これは、DP stepの変異影響への理解がより深い可能性を示しています

200
00:06:48,600 --> 00:06:51,640
更能抓住导致稳定性变化的关键因素
安定性変化の鍵となる要因をより的確に捉えられます

201
00:06:51,640 --> 00:06:52,840
这么说来
そういえば

202
00:06:52,840 --> 00:06:55,400
DP step的重要意义就很明显了
DPステップの重要性は明らかです

203
00:06:55,400 --> 00:06:56,240
是的
はい

204
00:06:56,240 --> 00:06:58,560
它提供了一种高效 准确
それは効率的で正確な

205
00:06:58,560 --> 00:07:01,480
而且主要是基于序列的预测方法
主に配列ベースの予測方法を提供します

206
00:07:01,480 --> 00:07:02,520
这就意味着
これはつまり

207
00:07:02,520 --> 00:07:05,240
研究人员可以更快 更广泛地去
研究者はより速く、広範囲に

208
00:07:05,240 --> 00:07:08,200
评估各种突变对蛋白质稳定性的影响
様々な変異がタンパク質安定性に与える影響を評価できます

209
00:07:08,200 --> 00:07:12,240
不需要再等那个又慢又贵的结构测定了
遅くて高価な構造解析を待つ必要がありません

210
00:07:12,240 --> 00:07:14,040
这对加速蛋白质设计
タンパク質設計の加速にとって

211
00:07:14,040 --> 00:07:17,080
还有煤工程要开发这些领域
そして酵素工学などの分野の発展において

212
00:07:17,080 --> 00:07:19,160
绝对是个巨大的推动力
確かに大きな推進力となります

213
00:07:19,160 --> 00:07:21,720
它克服了老方法的瓶颈
それは従来の方法のボトルネックを克服しました

214
00:07:21,720 --> 00:07:22,600
好
はい

215
00:07:22,600 --> 00:07:24,360
我来试着小结一下
まとめてみましょう

216
00:07:24,360 --> 00:07:27,160
DP step的核心就是
DPステップの核心は

217
00:07:27,160 --> 00:07:30,520
用了蛋白质大语言模型和交叉注意力
タンパク質大規模言語モデルとクロスアテンションを使用し

218
00:07:30,520 --> 00:07:36,200
再加上自我征留和反对称约束这两个独门密集
さらに自己教師あり学習と非対称制約という独自の工夫を加えることで

219
00:07:36,200 --> 00:07:39,800
让它能只靠序列就挺准的预测
配列のみで正確な予測が可能になります

220
00:07:39,800 --> 00:07:43,000
单个儿安计算突变对蛋白质稳定性的影响
個々の変異がタンパク質安定性に与える影響を計算できます

221
00:07:43,160 --> 00:07:46,120
关键是又快又准适用性海广
鍵は速くて正確で適用範囲が広いこと

222
00:07:46,120 --> 00:07:47,800
总结得很到位
とても的を射たまとめ方だ

223
00:07:47,800 --> 00:07:50,360
当然了研究肯定还在继续
もちろん研究はまだ続いている

224
00:07:50,360 --> 00:07:52,760
路门也提到了未来的风向
路門は将来の方向性にも触れていた

225
00:07:52,760 --> 00:07:57,000
比如说预测多个安计算同时突变的影响
例えば複数のアミノ酸計算が同時に変異する影響の予測

226
00:07:57,000 --> 00:07:59,560
这个在实际应用里其实更常见
これは実際の応用ではより一般的だ

227
00:07:59,560 --> 00:08:00,920
但肯定也更复杂
しかし確かにより複雑でもある

228
00:08:00,920 --> 00:08:01,720
对
そうだ

229
00:08:01,720 --> 00:08:03,320
一个变了就够麻烦了
一つ変わるだけでも十分厄介なのに

230
00:08:03,320 --> 00:08:04,600
好几个一起变
いくつも同時に変わると

231
00:08:04,600 --> 00:08:06,200
那相互影响就多了
相互影響がさらに増える

232
00:08:06,200 --> 00:08:07,000
是啊
そうだね

233
00:08:07,000 --> 00:08:07,960
另外呢
それから

234
00:08:07,960 --> 00:08:10,120
怎么把更多细节信息
どうやってより詳細な情報を

235
00:08:10,120 --> 00:08:12,440
比如测链的具体构向
例えば側鎖の具体的な構造

236
00:08:12,440 --> 00:08:13,800
几何解构啊
幾何学的配置など

237
00:08:13,800 --> 00:08:17,080
整合进来也是提高精度的方向
取り入れるかも精度向上の方向性だ

238
00:08:17,080 --> 00:08:17,880
所以呀
だから

239
00:08:17,880 --> 00:08:20,600
这里也留给你一个问题可以思考一下
ここで一つ考えてみるべき問題を残しておこう

240
00:08:20,600 --> 00:08:22,680
从预测单个儿点突变
単一の点変異を予測することから

241
00:08:22,680 --> 00:08:24,920
升级到预测多个点突变
複数の点変異を予測するようにアップグレード

242
00:08:24,920 --> 00:08:28,040
你觉得这个挑战的难度会增加多少
この挑戦の難易度はどのくらい増えると思いますか

243
00:08:28,040 --> 00:08:30,680
是限性的还是指数级的
線形的なのか指数関数的なのか

244
00:08:30,680 --> 00:08:33,880
这会给预测模型带来哪些全新的计算上
これは予測モデルにどのような新しい計算上の

245
00:08:33,880 --> 00:08:35,560
或者理论上的难题呢
または理論的な難題をもたらすでしょうか

246
00:08:35,560 --> 00:08:36,600
值得琢磨琢磨
じっくり考える価値がある

247
00:08:36,600 --> 00:08:39,000
确实是个值得思考的问题
確かに考える価値のある問題だ

248
00:08:39,000 --> 00:08:39,400
好
はい

249
00:08:39,400 --> 00:08:41,000
非常感谢今天的分享
本日のシェア、本当にありがとうございました

250
00:08:41,000 --> 00:08:42,360
也感谢你的收听
ご視聴もありがとうございます

251
00:08:42,360 --> 00:08:43,320
我们下次再会
また次回お会いしましょう
