你有没有停止思考如何这些小小的完全不明显的变化发生在维生素层层层内的变化特别是如何这些小小的变化可以有很大的影响在健康、医疗甚至企业上这种变化是很惊人的确实是基因均衡,那就是垃圾它的形状是重要的正确的更重要的是,连一个氨酸的变化,就一个小小的建筑物层层层都可以把这种稳定性放弃对,完全改变它的功能,或者缺乏它所以我们要如何预测这些变化,以及重要的是,它们的后果这件事实在是很难解决这就是我们今天要解决的问题这次深入的探索集中了一个非常有趣的新研究文章它介绍了一个计算方法叫DP-STABDP-STAB,对我们将探索如何这种新的方法终于能够破坏这是一项医学科技的非常重要的,复杂的挑战是的,我们的任务是为您解决DP-STAB不管您正在准备会议或者只是有兴趣的关于最新的科学或者可能只是有兴趣的关于AI的改变正是我们想给您展示DP-STAB的效果这些单个变化的均衡并重要的是,为什么它实际上重要因为这不只是学术的疑问并不是这是一个有真实的可能性的破坏对于医疗、工业、甚至更多的绿色企业来说好的,我们在谈谈细微细的细胞改变巨大的影响我们在DP-STAB的解决前我们先集中在问题上为什么这种改变是如此难以预测的是的,这就是奥特吉与达特尼姆的价值对,达特尼姆是吉布免费能量的改变是一个稳定的改变而DP-STAB是烧焦的改变知道这些改变是针对蛋白工程的烧焦或者设计新的药物完全不值得如果你想要使蛋白更稳定或者设计正确的药物你必须要预测这些东西但是,是的,正确性是一个巨大的障碍那么,什么是以前的试验方法历史上,有两个主要的路线首先,有设计基础的方法好的,很合理你看看3D的设计对,你需要一个高品质的细腻3D图表的蛋白需要正确的绿色线路线和一个引擎的线路线去看到改变一个小部分的部分有什么影响很合理,那是什么捷迹捷迹就是得到那个绿色线路线高品质的细腻通常是很困难甚至是不可能得到的所以这些方法在许多许多情况下并不适合没有细腻,没有预测好的,那是限制的那是什么选择另一个主要的方法是系统基础的方法这些方法比较适合大规模的东西因为你只需要蛋白酵素系统的细腻就是一个细腻基本的研究对,我们有很多细腻的数据比较容易得到但他们困难了,为什么他们困难了,是的因为基本上他们不能看到细腻的改变在区域中在变化的地方啊,好的所以即使整个细腻不改变形状对,那些细腻的改变和在附近的互动这些是比较坚定的而细腻方法他们只是不能能够实际地捕捉这些细腻他们错过了在当地的细腻中还有另一个问题对吧有关数据的问题哦,是的一个大问题数据不平衡意思是当我们看我们的测试数据时细腻变化的变化比变化的变化更常见这就是你经常想要的对吧一个更坚定的细腻对,所以数据用来训练预测模式对细腻变化的例子有很大影响这可以让模型变得更坚硬对让它们较少找到那些珍贵的稳定模式对模型可能只会学习通常会预测变化的变化因为那是他们经常看到的训练数据好的所以我们有失去的细腻无法看到地区变化的方法还有变化数据这真是一座山这实际上是为了需要一个新的方法好的所以进入DP步骤这里是非常有趣的地方这篇文章提出的是一个新的深入学习系统特别设计解决我们刚刚提出的所有限制这就是目标要得到你预期的基础基础方法但只用系统使它更加高级那它如何做到的那是主要的想法吗主要的创新是使用一个非常强大的重量语言模式PLLM就像AI模式写文章但为了细腻就像这样它被训练在真正大量的细腻运行计划中它学习细腻的语言如何变化细腻细胞如何相互联系特别是DP-STAB使用了一个叫ESM2的模式ESM2好的我听到过是的它是一种现实性的模式所以ESM2帮助DP-STAB理解细腻运行计划但是这里有个聪明的部分它也可以用来从细腻运行计划采取证明信息证明信息像形状信息有点它可以预测一些细腻运行计划的图表基本上哪些氨酸物可能在3D图形中图形图形中相互联系啊所以它是预测细腻运行计划不需要实际的试验组织对它是不仅是在看细腻运行的图形而是在看细腻运行的图形哇好的很聪明但是你也提到一些秘密的方法具体的方法对DP-STAB有几个重要的东西非常出色首先是这个自动细腻运行计划的方法自动细腻运行什么意思实际上它似乎很复杂但它是一个教导自己改善假设呃模特做出某些数据的预测然后它使用自己的预测作为增加训练但只有自信的所以它用自己的出口细腻运行计划差不多它增加训练数据帮助模特学习更多的细腻运行并使它更好地在细腻运行使用自己的知识进行新的细腻运行它从未见过好的那是一部分那是另一部分吗另一个重要的方法就是对比的限制对比的限制好的把这些都分析下来它根据基本的逻辑如果您把一种胃酸从A到B变成了某种程度的稳定比如说它减少了两个细腻好的那么逻辑上从B到A应该做到相反的事情对吧它应该减少两个细腻运行的稳定很合理Daiichi应该翻译为正确Daiichi B应该等于-DBA但是年轻人的模特不经常遵循这个规则真的吗这似乎是一个基本的权利是的但是他们的建筑并没有扩展它DP-STAB明显地建立了这个限制它使模特学习和尊重这个基本的逻辑或者说反逆逻辑这让预测比较实际实际和可靠更加稳定是的它确保内部的适当性好的那么我们有PLMM为选项自动解决为了学习更好还有这个适当逻辑的限制为了适当逻辑我们可以继续谈谈吗这些组件怎么组成的当然DP-STAB基本上有三个主要模组在一起第一个是逻辑研究器那是ESM2的部分是的是ESM2的功能它的工作是把成分运输运输到那些充满生命性的资料并且预测那些联络图我们谈论过选择的组织资料这是最初的计算机明白接下来呢接下来是隔离参数研究器这里是在逻辑研究器中专注在当地环境对是的它使用了逻辑研究器的选择方式想起给模组能够专注在逻辑研究器和其重要的邻居的能够猜测旁边的逻辑是最重要的并且从它进行讯息是的具体性它也可以掌握长远的产生有时候逻辑研究器在连续中都合成并互动在3D空间这个模组帮助掌握那些重要的非地区效果那些更简单的方法经常错失好那么逻辑研究器然后专注在旧地和长远的资料最后的步骤是模组转移这个模组取得所有的资料包括原来的核酸生物和变化的模组组成对它从两个的功能组成环境状态例如氧气和温度尤其为长远的预测因为坚定性也可以依据这些因素然后它取得最后的预测长远的或帮助正确它综合所有要做最终的坚定性改变好的那很详细的那么这个万亿美元的问题是它实际上有多好这些组成的组成有多复杂对终于有很强劲的结果DPSTAB似乎正在设立一个新的状态在整个图表上对对我们先谈谈AG预测那个变化的数据DPSTAB的预测比之前的方法更低低的预测意味着更准确更准确RMSE是一个平均错误下降低于0.93KMAE另一个错误的预测是低于0.68K低于是更好的预测比实际测试非常好预测的比例非常高预测比例和实际比例非常高SEC的Sperman预测达到0.86PCC的Piercing预测达到0.84PCC低于1是更好所以那些是很棒的预测哇好的那是继续的价值什么关于级别的预测例如是稳定不稳定还是平均这是你想回答的实际问题而这里也有DPStab做得非常好它的级别准确是超过74%所以约三四次它正确显示无论是否有变化弱化或不影响蛋白质对在他们的测试中它正确地列出了684除了922的情况这是很大的改善而且非常有用在可能的变化在药物设计或氧化工程削弱了医疗工作我想的重点省下时间和资源你也提到团队的预测溶化温度是的在那里的表现也被认为是最强的或至少相比较好的最佳存在的方法所以它在多层层上我们能看到它在行动上吗像具体的例子是的文件显示DP研究每个蛋白质的效果是实际设计工作相比于更老的方式他们看到选择改善的9%在SEC7.5%在PCC和几乎20%在蛋白质证明所以它不仅是平均的它也比较适合你可能在具体的情况上正确他们展示了一个试验2PTLA的A34G变化DP研究预测一个均衡减少1.2kM在真实价值试验价值是2.1kM所以很近但重要的是前面的主要方法预测它是正常的完全失去均衡为什么DP研究正确它提供了解释是的那是美妙的分析指出代替A与小G的均衡与近距离的均衡例如Y50和F36它指出结构原因啊提供理解不只是数字正确另一个例子106XA核心H51ADP研究预测0.8kM实际是0.7kM再一次很近而前面的方法前面的方法错误地称它稳定DP研究指出大量的氧化银合因此消除酸化银合它看到这些重要的氧化银合互动它似乎更好地捕捉它这些特定的正确的理解是蛋蛋所以这些大型计算机能够解决酸化银合的问题很大还有DMS数据提供的酸化银合数据是预测均等变化最高的数据预测最大效果通常是实际高试验DGA价值意味着它最强的预测可能会出现正确的稳定变化研究员经常寻找的它像是找到好东西的更好的 filter找到酸化银合更多的效果OK所以表现明显高级回顾为什么你提到这两个重要的策略自动探测和相反的限制他们实际看起来非常重要根据酸化银合研究他们测试研究这些部分取消表现明显下降DG预测SCCPCC下降约3%错误RMSEMAE6%4%相互所以并不是一个小改变实际上模组学得更好绝对它提升总结他们甚至显示在测试完全无线的设备进行不同的技术增加表现更多这也使模组更坚硬OK和相反限制再提醒A to B改变是BDA的相反对那基本的适当适当老人模组有多坚硬挺坚硬有时候A B B A的预测可能是不一样的不坚硬如果你依靠预测这些预测增加限制DPSTAB修改大幅使模组遵循这个规则DPSTAB使预测这些向前反转变化变得更小出现几乎完全相反应该是这就让整个系统更有信心实用实用正确你可以依靠预测相反的关系持续预测增加坚硬预测所以我们把这些都拉起来这是什么大图这是什么DPSTAB的主要优势在什么标题上首先它是连续那是巨大不需要那些难得得到3D的结构使它非常适合是的第二它能够正确地预测坚硬的变化根据PLLM来说它能够看到巨大的变化根据PLLM它能够看到巨大的变化根据巨大的变化对第三它能够评估坚硬的坚硬的坚硬的变化例如维生素维生素能够提供有效的变化所以苹果工程师或生物工程师它提供快速实用和高级的工程它能够迅速设计更好的维生素无论是治疗诊断或工程减少猜测更快的发现这似乎是一个重要的进步是的接下来这些未来的挑战或者未来的方向在这些领域肯定有未来的挑战一个是更细腻的细节例如细节细节细节细节细节细节细节细节细节细节细节细节细节细节细节细节细节细节细节细节细节细节细节细节细节细节细节细节是一个很好的例子如何有精致的智能智能像这些大语言模式可以去解决这些有关生物的困难绝对是这样的它显示了这些系统的秘密当你想到这些可能性能够精准地制造特定工作的生物我意思是设计药品比较稳定长期在身体里专注于真正能够在最基础的程度上设计生物这真是开启了人的想象什么其他超级复杂的生物图案除了基础稳定我们还能怎么解决使用这些深入学习的工具什么是最后的大线链那是个兴奋的问题可能性似乎很大